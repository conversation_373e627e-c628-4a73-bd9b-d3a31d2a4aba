import { getOpenAppFlag, setLoggedIn, setUserInfo } from "@/gdata";
import { getImageURL, pushWebView, showModal } from "@/tools";
import {
  Button,
  Image,
  Swiper,
  SwiperItem,
  Text,
  View,
} from "@tarojs/components";
import Taro, { RewardedVideoAd } from "@tarojs/taro";
import React, { Component } from "react";
import * as Pages from "@/env/pages";
import { AtModal, AtModalAction, AtModalContent } from "taro-ui";

import { OrderCategory } from '@/enums';
import { filterSubscribeMessage } from "@/util";
import { loginOut, requestAuth } from "../../api/modules/auth";
import {
  doSign,
  getMycenterList,
  getUserGold,
  getUserInfo,
  getUserOrderList,
  getUserVipInfos,
  getVipAdsInfo,
  getXcxMessagesV2,
  signInfo as getSignInfo,
} from "../../api/modules/user";
import "./index.scss";

interface Props {
  children?: any;
}
interface MyOrderArr {
  category: string;
  count: number;
  description: string;
  hideFlag: number;
  id: number;
  image: string;
  name: string;
  permCode: number;
  sid: string;
  sort: number;
  url: string;
}
interface State {
  // APP_VERSION: string,
  userName: string;
  avatar: string;
  isVip: boolean;
  goldNum: number;
  isOpened: boolean;
  isSign: boolean;
  loggedIn: boolean;
  couldOpenApp: boolean;
  vipImage: string;
  vipImageUrl: string;
  myOrderArr: any[];
  currentIndex: number;
  centerArr: MyOrderArr[];
  recommendArr: MyOrderArr[];
  vipData: Array<any>;
}
// interface Index {
//   children?: React.ReactNode
// }
export default class Index extends Component<Props, State> {
  // config: Config = {
  //   navigationBarTitleText: '',
  //   enablePullDownRefresh: true,
  //   backgroundTextStyle: 'dark',
  //   navigationBarBackgroundColor:'#f3f3f3'
  // }
  isWeapp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP
  isAlipay = Taro.getEnv() === Taro.ENV_TYPE.ALIPAY
  constructor(props: Props) {
    super(props);
    this.state = {
      // APP_VERSION: "",
      userName: "",
      // 默认头像
      avatar: getImageURL("Fr9WEh0tOeRea8vZjSgXgr2_K0By"),
      isVip: false,
      goldNum: 0,
      isOpened: false,
      isSign: true,
      loggedIn: false,
      couldOpenApp: getOpenAppFlag(),
      vipImage: "",
      vipImageUrl: "",
      myOrderArr: [],
      currentIndex: 0,
      centerArr: [],
      recommendArr: [],
      vipData: [],
    };
  }
  // onShareAppMessage(res) {
  //   return {
  //     // title: '交广领航',
  //     path: '/pages/index/index'
  //   }
  // }
  // onShareTimeline() {
  //   return {
  //     title: '交广领航-服务爱车生活',
  //   }
  // }
  getPageData() {
    getUserInfo()
      .then((userInfo) => {
        this.setState({
          userName: userInfo.name,
          avatar: getImageURL(
            userInfo.portrait || "Fr9WEh0tOeRea8vZjSgXgr2_K0By"
          ),
          isVip: userInfo.vip,
          loggedIn: true,
        });
        setUserInfo(userInfo);
        this.getUserContent();
        return Promise.all([getUserGold(), getSignInfo()]).then(
          ([goldInfo, signInfo]) => {
            this.setState({
              goldNum: goldInfo.accountCount,
              isSign: signInfo.signStatus == 1,
            });
          }
        );
      })
      .catch((err) => {
        console.error(err);
      });
  }

  getUserContent() {
    return Promise.all([
      getUserVipInfos(),
      getUserOrderList(),
      getMycenterList(),
      getVipAdsInfo(),
    ]).then(([vipImageInfo, orderInfo, centerInfo, vipCards]) => {
      this.setState({
        vipImage: vipImageInfo.image ? getImageURL(vipImageInfo.image) : null,
        vipImageUrl: vipImageInfo.url,
        myOrderArr: orderInfo ? this.groupList(orderInfo, 5) : [],
        centerArr: centerInfo.personalCenterList
          ? centerInfo.personalCenterList
          : [],
        recommendArr: centerInfo.recommendItemList
          ? this.groupList(centerInfo.recommendItemList, 3)
          : [],
        vipData: vipCards || [],
      });
    });
  }

  componentWillMount() {
    requestAuth()
      .then(() => {
        this.getPageData();
      })
      .catch((err) => {
        this.getUserContent();
      });

    // 展示小程序版本号
    // if (process.env.TARO_ENV === 'weapp') {
    //   const info = Taro.getAccountInfoSync();
    //   // console.log('小程序版本++++', info)
    //   const mpVersion = info.miniProgram.version;
    //   // console.log('info: ', info)
    //   if (mpVersion) {
    //     this.setState({
    //       APP_VERSION: `v${mpVersion}`,
    //     })
    //   }
    // }
  }

  onPullDownRefresh() {
    requestAuth()
      .then(() => {
        this.getPageData();
        Taro.stopPullDownRefresh();
      })
      .catch((err) => {
        Taro.stopPullDownRefresh();
      });
  }

  componentDidMount() {}

  componentWillUnmount() {}

  componentDidShow() {
    this.setState({
      couldOpenApp: getOpenAppFlag(),
    });
    requestAuth()
      .then(() => {
        this.getPageData();
      })
      .catch((err) => {
        console.error(err);
      });
  }

  componentDidHide() {}
  goLogin() {
    Taro.navigateTo({
      url: "/pages/login/index",
    });
  }
  // 订单中心
  goOrderCenter() {
    if (!this.state.loggedIn) {
      this.goLogin();
      return;
    }
    Taro.navigateTo({
      url: "/pages/platform/order",
    });
  }
  handleRecommendClick(item): void {
    // 跳转小程序页面
    if (item.name != '优惠劵') {
      this.toPage(item.url);
      return;
    }
    let that = this
    getXcxMessagesV2().then(serviceArr => {
      let subs = filterSubscribeMessage(serviceArr, 'coupons')
      if (subs.length) {
        Taro.requestSubscribeMessage({
          tmplIds: subs,
          entityIds:[], // 解决ts报错，请忽略 模板小程序 appId，仅在服务商代调用场景下需要传入
          complete: function(success) {
            setTimeout(function() {
              that.toPage(item.url);
            }, 30);
          },
          success: function(success) {
            console.log("订阅成功", success);
          },
          fail: function(err) {
            console.log("订阅失败", err);
          }
        });
        return
      }
      that.toPage(item.url);
    })
    .catch((err) => {
      that.toPage(item.url);
    });
  }
  toPage(url: string, shouldLogin = true): void {
    // console.log(this.state.loggedIn);
    if (shouldLogin && !this.state.loggedIn) {
      this.goLogin();
      return;
    }
    pushWebView(url, { share: false });
  }
  toMpPage(item): void {
    // 跳转小程序页面
    if (item.permCode == 11) {
      let _url = /^\/.*/.test(item.url) ? item.url : `/${item.url}`; // 路径斜杠开头，兼容管理端配置时出错
      Taro.navigateTo({
        url: _url,
      });
      return;
    }
    this.toPage(item.url);
  }
  gotoVipPage(url: string) {
    pushWebView(url, { share: false });
  }
  loginOut() {
    showModal("退出登录", "您即将退出当前交广领航帐号", {
      // cancelText: '取消',
      // confirmText: '确定',
      success: (ret) => {
        // console.log(ret);
        if (ret.confirm) {
          loginOut()
            .then(() => {
              this.setState({
                loggedIn: false,
              });
              setLoggedIn(false);
              setUserInfo({});
              this.getUserContent();
              requestAuth(true).catch((res) => {
                this.goLogin();
              });
            })
            .catch((err) => {
              console.error(err);
            });
        }
      },
    });
  }

  getSign() {
    this.setState({
      isOpened: true,
    });
  }

  getSignGold() {
    // 普通签到
    doSign(2)
      .then(() => {
        showModal("提示", "金币已入账！");
        this.getPageData();
      })
      .catch((err) => {
        showModal("提示", err.toString());
      });
  }
  // 隐藏入口，便于测试视频广告
  onLongPressSignButton(e) {
    this.getSign();
  }
  launchAppError(err) {
    showModal("错误", err && err.errMsg);
  }
  watchVideo() {
    // 在页面onLoad回调事件中创建激励视频广告实例
    // eslint-disable-next-line no-undef
    if (process.env.TARO_ENV === "weapp") {
      if (Taro.createRewardedVideoAd) {
        // 在页面中定义激励视频广告
        // eslint-disable-next-line no-undef
        const videoAd: RewardedVideoAd & {
          closeHandler?: any;
        } = Taro.createRewardedVideoAd({
          adUnitId: "adunit-0f238c9b13a970f7",
        });
        // 多次播放激励视频广告要先卸载之前的监听事件，否则回造成多次onClose回调
        try {
          if (videoAd.closeHandler) {
            videoAd.offClose(videoAd.closeHandler);
            console.log("---videoAd.offClose 卸载成功---");
          }
        } catch (e) {
          console.log("---videoAd.offClose 卸载失败---");
          console.error(e);
        }
        videoAd.onLoad(() => {});
        videoAd.onError((err) => {});
        videoAd.closeHandler = (res) => {
          // 用户点击了【关闭广告】按钮
          if ((res && res.isEnded) || res === undefined) {
            doSign(1)
              .then((response) => {
                showModal("提示", "金币已入账！");
                this.getPageData();
              })
              .catch((err) => {
                showModal("提示", err.toString());
              });
          }
        };
        videoAd.onClose(videoAd.closeHandler);
        // videoAd.onClose((res) => {
        //   if (res && res.isEnded || res === undefined) {
        //     doSign(1).then(res => {
        //       showModal('提示', '金币已入账！');
        //       this.getPageData();
        //     }).catch(err => {
        //       showModal('提示', err.toString());
        //     })
        //   }
        // })
        // 用户触发广告后，显示激励视频广告
        videoAd.show().catch(() => {
          // 失败重试
          videoAd
            .load()
            .then(() => videoAd.show())
            .catch((err) => {
              console.log("激励视频 广告显示失败");
            });
        });
      }
    }
  }
  groupList(array: MyOrderArr[], count: number) {
    let index = 0;
    let newArray = [] as any[];
    while (index < array.length) {
      newArray.push(array.slice(index, (index += count)));
    }
    return newArray;
  }
  swiperChange(e) {
    this.setState({ currentIndex: e.detail.current });
  }
  render() {
    let {
      userName,
      avatar,
      isVip,
      goldNum,
      isOpened,
      vipImage,
      vipImageUrl,
      myOrderArr,
      currentIndex,
      centerArr,
      recommendArr,
      vipData,
    } = this.state;

    const loggedInfo = (
      <View className="user-info">
        <View className="user-image-avatar">
          <Image src={avatar} className="user-avatar" />
          {isVip && <Text className="user-vip" />}
        </View>

        <View className="user-content">
          <View className="user-right">
            <Text className="user-name">{userName}</Text>
            {/* <View
              className="sign"
              onLongPress={this.onLongPressSignButton.bind(this)}
              onClick={() => {
                // this.getDoubleSignGold();
                if (this.state.isSign) {
                  showModal("提示", "您今天已经签过到了！");
                  return;
                }
                this.setState({
                  isOpened: true
                });
              }}
            >
              签到领金币
            </View> */}
          </View>
        </View>
      </View>
    );
    const notLoggedInfo = (
      <View className="user-info" onClick={this.goLogin.bind(this)}>
        <Image
          src={getImageURL("Fr9WEh0tOeRea8vZjSgXgr2_K0By")}
          className="user-avatar"
        />
        <View className="user-content not-loggedin">
          <View className="name">登录/注册 &gt;</View>
        </View>
      </View>
    );

    const menuList = myOrderArr.map((item: any, index: number) => {
      return (
        <SwiperItem key={index + 0}>
          <View className="swiperBox">
            {item.map((element: any) => {
              return (
                <View
                  className="menu-item"
                  key={element.id}
                  onClick={this.toPage.bind(this, element.url)}
                >
                  <View className="menu-item-img">
                    <Image src={getImageURL(element.image)} className="img" />
                  </View>
                  {element.count > 0 && (
                    <Text className="menu-item-badge">
                      {element.count > 99 ? "99+" : element.count}
                    </Text>
                  )}
                  <Text className="menu-item-text">{element.name}</Text>
                </View>
              );
            })}
          </View>
        </SwiperItem>
      );
    });

    const centerList = centerArr.map((item: any) => {
      return (
        <View
          className="center-item"
          onClick={this.toMpPage.bind(this, item)}
          key={item.id}
        >
          <View className="center-item-img">
            <Image src={getImageURL(item.image)} className="img" />
          </View>
          <Text className="center-item-text">{item.name}</Text>
        </View>
      );
    });
    const recommendList = recommendArr.map((item: any, index: number) => {
      return (
        <SwiperItem key={index + 0} className="tab-top-item">
          <View className="tabList">
            {item.map((element: any) => {
              return (
                <View
                  className="tab-item"
                  key={element.id}
                  // onClick={this.toPage.bind(this, element.url)}
                  onClick={this.handleRecommendClick.bind(this, element)}
                >
                  <View className="tab-img">
                    <Image src={getImageURL(element.image)} className="img" />
                  </View>
                  <Text className="tab-label">{element.name}</Text>
                </View>
              );
            })}
          </View>
        </SwiperItem>
      );
    });

    return (
      <View>
        <AtModal isOpened={isOpened}>
          <AtModalContent className="at-modal-content">
            <Button
              style="background: #398eff;color: #fff;"
              onClick={this.getSignGold.bind(this)}
            >
              签到领金币
            </Button>
            {/* <Button
              style="margin-top: 10px;background: red;color: #fff;"
              onClick={this.watchVideo.bind(this)}
            >
              看视频领双倍
            </Button> */}
          </AtModalContent>
          <AtModalAction>
            {" "}
            <Button
              onClick={() => {
                this.setState({
                  isOpened: false,
                });
              }}
            >
              关闭
            </Button>{" "}
          </AtModalAction>
        </AtModal>
        <View className="userInfo">
          {this.state.loggedIn ? loggedInfo : notLoggedInfo}
        </View>

        <View className="box">
          {vipData.length > 0 && (
            <View v-if="vipData.length" className="vip-box spacing">
              {vipData.map((item: any, index: number) => (
                <View className="img-item" key={index}>
                  <Image
                    className="vipImg"
                    mode="widthFix"
                    src={getImageURL(item.image)}
                    onClick={this.gotoVipPage.bind(this, item.url)}
                  />
                  <text className="img-name">{item.title}</text>
                </View>
              ))}
            </View>
          )}

          {recommendArr.length > 0 && (
            <View className="blockBg spacing">
              <View className="swiperBody">
                <Swiper
                  className="tab-top"
                  onChange={this.swiperChange.bind(this)}
                  adjustHeight="first"
                >
                  {recommendList}
                </Swiper>
              </View>
            </View>
          )}

          {myOrderArr.length > 0 && (
            <View className="blockBg spacing">
              <View className="titleBox">
                <Text className="title">我的订单</Text>
                <Text
                  className="rightTitle"
                  onClick={this.goOrderCenter.bind(this)}
                >
                  全部订单
                </Text>
              </View>
              <View className="swiperBody">
                <Swiper
                  className={this.isAlipay ? "myOrder myOrderAli" : "myOrder"}
                  onChange={this.swiperChange.bind(this)}
                  adjustHeight="first"
                >
                  {menuList}
                </Swiper>
                {/* 自定义指示点 */}
                {myOrderArr.length > 1 ? (
                  <View className="spot-pagination">
                    {myOrderArr.map((item: any, index: number) => (
                      <View
                        key={index}
                        className={
                          "spot-pagination-bullet " +
                          (currentIndex == index
                            ? "spot-pagination-bullet-active"
                            : "")
                        }
                      />
                    ))}
                  </View>
                ) : (
                  ""
                )}
              </View>
            </View>
          )}

          {centerArr.length > 0 && (
            <View className="blockBg spacing">
              <View className="titleBox">
                <Text className="title">我的服务</Text>
              </View>

              <View className="centerBox">{centerList}</View>
            </View>
          )}
          {/* <View className="list">
            {list.map(item => {
              return (
                <View
                  className="item"
                  onClick={this.toPage.bind(this, item.url)}
                >
                  <View className={["icon", item.icon].join(" ")}></View>
                  <Text className="item-text">{item.name}</Text>
                  <View className="go"></View>
                </View>
              );
            })}
          </View> */}
          <View className="blockBg">
            <View className="titleBox">
              <Text className="title">其他</Text>
            </View>
            <View className="centerBox">
              <View
                className="center-item"
                onClick={this.toPage.bind(this, Pages.HELP, false)}
              >
                <View className="center-item-img">
                  <Image
                    src={getImageURL("Fo1D2dIePRukoy91ik08tBuTCPy-")}
                    className="img"
                  />
                </View>
                <Text className="center-item-text">客服帮助</Text>
              </View>
              {/* <View
                className="center-item"
                // onClick={this.toPage.bind(this, item.url)}
              >
                <View className="center-item-img">
                  <Image
                    src={getImageURL("Fr-gifb4S-n9qZkUIyFeehL35SL1")}
                    className="img"
                  />
                </View>
                <Text className="center-item-text">扫一扫</Text>
              </View> */}
              {/* <View
                className="center-item"
                // onClick={this.toPage.bind(this, item.url)}
              >
                <View className="center-item-img">
                  <Image
                    src={getImageURL("FroO3Q-LUHx7bQMWRqKN3fU4Ik8e")}
                    className="img"
                  />
                </View>
                <Text className="center-item-text">分享好友</Text>
              </View> */}
              {this.state.loggedIn && (
                <View
                  className="center-item"
                  onClick={this.loginOut.bind(this)}
                >
                  <View className="center-item-img">
                    <Image
                      src={getImageURL("FnZlIJ6Cc49GRWlkZh2Tl9JDcsVD")}
                      className="img"
                    />
                  </View>
                  <Text className="center-item-text">退出登录</Text>
                </View>
              )}
            </View>
          </View>
        </View>
        {/* <View className='page-bottom'>{this.state.APP_VERSION}</View> */}
        {/* this.state.loggedIn && <View className='btn' onClick={this.loginOut.bind(this)}>
              <Text className='btn-txt'>退出登录</Text>
            </View> */}
        {/* {this.state.couldOpenApp && (
          <View className='float-btn' hoverClass='float-btn-hover'>
            <Button
              className='btn-openapp'
              type='default'
              open-type='launchApp'
              app-parameter='jglh-weapp'
              onError={this.launchAppError.bind(this)}
            >
              <Text>打开</Text>
              <Text>App</Text>
            </Button>
          </View>
        )} */}
      </View>
    );
  }
}
