{"extends": ["taro/react"], "rules": {"no-undef": 0, "import/first": 0, "space-infix-ops": 1, "array-bracket-spacing": 0, "object-curly-spacing": ["error", "always"], "spaced-comment": 2, "no-unused-vars": 0, "react/no-unused-state": 0, "react/sort-comp": 0, "react/self-closing-comp": 1, "react/no-multi-comp": 0, "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx", ".tsx"]}], "@typescript-eslint/no-unused-vars": [1, {"varsIgnorePattern": "React"}], "@typescript-eslint/no-undef": 0, "@typescript-eslint/explicit-function-return-type": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-explicit-any": 0, "jsx-quotes": ["error", "prefer-double"], "import/prefer-default-export": 0, "@typescript-eslint/camelcase": 0, "@typescript-eslint/ban-ts-ignore": 0, "@typescript-eslint/no-inferrable-types": 0, "@typescript-eslint/no-shadow": 0}}