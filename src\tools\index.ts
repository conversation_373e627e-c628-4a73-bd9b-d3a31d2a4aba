import { getSessionId, getLoggedIn, getUserType } from '@/gdata';
import { addURLSearchParams } from './url';
import AMap from './amap-wx'

import Taro from "@tarojs/taro";

export * from './modal';
export * from './url';

function getFullImagePath(key, type = '') {
  if(!key) {
    return key;
  }
  if (/^http(s)?:/.test(key)) return key;
  return `https://img.jgrm.net/${key}${type}`;
}

/**
 * 根据资源获取图片url，支持七牛云图片id，或图片url，图片路径
 * 曾经有老版本app（3.8.x）预览图片不支持https协议的图片地址
 * @param {string} value 图片ID或路径
 * @param {string} type 可选，七牛云图片参数
 * @param {string} protocol 图片协议
 */
export function getImageURL(value, type = '?', protocol = 'https:') {
  // console.log(...arguments);

  // file, data, bolb, http, https ...
  if (/^\w+:/.test(value)) return value;

  // `./` 开头 认为是相对路径
  if (/^\./.test(value)) return value;

  // `/` 开头认为是绝对路径
  if (/^\/\//.test(value)) return value;

  // `img/` 目录开头的，认为是相对路径
  if (/^img\//.test(value)) return value;

  // android某版本有些图片id是路径，如/storage/6633-6466/DCIM/Camera/20161002_154738.jpg
  if (/^\//.test(value) && !/storage/.test(value)) return value;

  // 其他值都认为是
  return `${protocol}//img.jgrm.net/${value}${type}`;
}

/**
 * 根据资源获取音频url
 * @param {string} value 资源ID或路径
 * @param {string} type 可选，七牛云图片参数
 * @param {string} protocol 协议
 */
 export function getAudioURL(value, protocol = 'https:') {
  // file, data, bolb, http, https ...
  if (/^\w+:/.test(value)) return value;

  // `/` 开头认为是绝对路径
  if (/^\/\//.test(value)) return value;

  // A_版本号_七牛资源id 开头认为是app聊天室发送的, A_443_26d0367aab1147e78164c7e3a7f6704f,
  if (/^A_[0-9]{3,4}_/.test(value)){
    const model = Taro.getSystemInfoSync().model;
    if (/iphone/i.test(model)){
      return `${protocol}//dev.jgrm.net/Radio/speex/audio?resourceId=${value}`
    }
    return `${protocol}//dev.jgrm.net/Radio/speex/audio/for/xcx?resourceId=${value}`
  };
  // 其他值都认为是
  return `${protocol}//audio1.jgrm.net/${value}`;
}

function replaceHost(u, h) {
  try {
    let res = u.replace('https://dev.jgrm.net', h)
      .replace('https://radio.jgrm.net', h)
      // .replace('/actions/app/mcar', '/jglh-webapp/dist');
    return res;
  } catch(err) {
    console.error(err);
    return u;
  }
}

/**
 * 根据path获取页面url地址
 * @param {string} path 页面目录
 */
export function getPageURL(path: string) : string {
  return `${BASE_URL}${path}`;
}

export function getAppURL(url: string, loggedIn: boolean) : string {
  return getWebViewURL(url, loggedIn);
}

/**
 * 获取webview的url地址
 * @param url 跳转链接
 * @param withAuthorization 是否携带认证信息，已登录状态下会先跳转到中转授权页
 */
export function getWebViewURL(url: string, withAuthorization : boolean = getLoggedIn() ) : string {
  if (!url) return url;
  if (!/radio.jgrm.net/.test(BASE_URL)) {
    url = replaceHost(url, BASE_URL);
  }
  // debugger
  console.log('getWebViewURL: ', url);
  const url2 = addURLSearchParams(url, {
    utm_source: 'WEIXIN_MP',
    utm_medium: 'jglh',
  })
  if (withAuthorization) {
    console.log(withAuthorization)
    const sessionId = getSessionId();
    const userType = getUserType();
    const authURL = getPageURL(`/actions/app/wechat/jump.html?token=${sessionId}&utype=${userType}&jumpUrl=${encodeURIComponent(url2)}`)
    return authURL;
  }
  return url2;
}

// 解析url
function parseUrl(url) {
  if (!url) {
    return {
      hash: '',
      orgin: '',
      params: {}
    };
  }
  var params = url.split('#');
  let hash = '';
  if (params[1]) {
    hash = params[1];
  }
  const urlArr = params[0].split('?');
  const paramsArr = {};
  if (urlArr[1]) {
    urlArr[1].split('&').forEach((item) => {
      var tmp = item.split('=');
      paramsArr[tmp[0]] = tmp[1];
    });
  }


  return {
    hash: hash,
    orgin: urlArr[0],
    params: paramsArr
  }
}
/**
 * @description: 版本号比较
 * @params v1 版本号
 * @params v2 版本号
 * @return {Number} 1 => v1 > v2, 0 => v1 == v2, -1 => v1 < v2
 */
export function compareVersion(v1, v2) {
  v1 = v1.split('.')
  v2 = v2.split('.')
  const len = Math.max(v1.length, v2.length)

  while (v1.length < len) {
    v1.push('0')
  }
  while (v2.length < len) {
    v2.push('0')
  }

  for (let i = 0; i < len; i++) {
    const num1 = parseInt(v1[i])
    const num2 = parseInt(v2[i])

    if (num1 > num2) {
      return 1
    } else if (num1 < num2) {
      return -1
    }
  }

  return 0
}

export { getFullImagePath, parseUrl }

/**
 * @description: 获取当前位置逆地理编码
 * @param {*} location 经纬度
 * @return {*} string
 */
export function reverseGeocoder(location) : Promise<any> {
  if (!location) return Promise.reject('location is required');
  const { latitude, longitude } = location;
  const AMAP_KEY = '7cf8afd9ccf52328f502624c0365401c';
  let myAmapFun = new AMap.AMapWX({ key: AMAP_KEY });
  return new Promise((resolve, reject) => {
    myAmapFun.getRegeo({
      location: `${longitude},${latitude}`,
      success: function(data){
        const { regeocodeData } = data && data[0];
        let geoData = {
          province: regeocodeData?.addressComponent?.province,
          city: regeocodeData?.addressComponent?.city,
          district: regeocodeData?.addressComponent?.district,
          address: regeocodeData?.formatted_address,
        }
        resolve(regeocodeData ? geoData : '')
      },
      fail: function(info){
        reject(info)
      }
    })
  })
}
