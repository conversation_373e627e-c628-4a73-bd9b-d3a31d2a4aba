.navbar2 {
  // background: transparent !important;
  background: transparent !important;
}
.navbar {
  position: fixed;
  z-index: 2;
  top: 0;
  width: 100%;
  background-color: #f3f3f3;
  // color: #fff;
  overflow: hidden;
  box-sizing: border-box;
  // background: -webkit-linear-gradient(top,rgb(233, 232, 232),#fff);
  // background: #398eff;
  &::after {
    border: none;
  }
  .title, .at-nav-bar__title {
    color: #000;
    // color: #fff;
    height: 100%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  .at-nav-bar__right-view{
    padding: 9PX 5PX;
  }


}

page {
  height: 100%;
  background: #F2F4F5;
}

.index {
  background-repeat: no-repeat;
  background-size: cover;
  background-color: #f2f4f5;
}
