import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { View, Image, Text } from '@tarojs/components'
import { Icon, Popup } from "@antmjs/vantui";
import { pushWebView, reverseGeocoder, getAudioURL } from "@/tools";
import { getImageURL } from "@/util";
import { formatRelativeTime } from "@/util/time";

import Audio from "@/pages/live/components/LiveMessage/MessageItem/components/Audio";
import styles from "@/pages/live/components/LiveMessage/MessageItem/index.module.less";
import './index.scss'


interface Props {
  showPop: boolean,
  currentMessage: any,
  onPlayAudio: Function,
  onClose: Function,
  playStatus: boolean;
}

interface State {
  markerIcons: string[];
  trafficTypes?: any[];
}

export default class Index extends Component<Props, State> {


  constructor(props) {
    super(props);

    this.state = {
      markerIcons: [
        "Fmo8T6lD4zzAdUDFOU3cA7RegK1f",
        "Fv8OVPvy4jxbG7S_Rd5ebb5XJqZZ",
        "Finod3rc9sekJUJtuFT1NaXB0v-g",
        "FolgpiIBMuKMGrvoZP-0D_nTsqag",
        "FjPy1BpE2pF3Il7M7VskV6KSqGfv",
        "FtP5GKB5XjQgC5cAYFXPLeUag1sJ",
        "FoGdMInsv0AflB6e9qc3_sB432P6",
        "FpSGTl7ZHlUfYqDeuQ-QQvm8eGrL",
        "Fs4jJM4nOE8kqEc_lO0yZR1CovA6",
      ],
      trafficTypes: [
        {
          icon: 'FifxNhGNpNEOEfhwgUlQtgIz8vIv',
          text: "道路通畅",
          value: 1,
        },
        {
          icon: 'FmaBOW1tRkfXYj6kBlFBpEmJWwZ1',
          text: "道路拥堵",
          value: 2,
        },
        {
          icon: 'FnjOyi_k2w61j42nQr4c7YmJOC4l',
          text: "交通事故",
          value: 3,
        },
        {
          icon: 'Fna3jROHUgrJffoxYve0FJiHEvVl',
          text: "设施故障",
          value: 6,
        },
        {
          icon: 'Fiq95pIB2UxdfXCOJhGkvH4rqngq',
          text: "道路施工",
          value: 4,
        },
        {
          icon: 'Fj8qoTX_7iqy9WiFrQISsW_pN-5S',
          text: "交通管制",
          value: 7,
        },
      ],
    };
  }

  componentDidMount () {

  }

  componentWillUnmount () { }

  componentDidShow () {


  }

  componentDidHide () { }
  toPage(url: string) {
  }

  handleClose() {
    this.props.onClose();
  }

  handlePlayAudio(url) {
    this.props.onPlayAudio(url);
  }

  handleMarkerPopupClose() {
    this.props.onClose();
  }

  handleMarkerPopupClickOverlay() {
    this.props.onClose();
  }

  handleImageClick() {
    let { currentMessage } = this.props;
    if (currentMessage.type == 5) {
      Taro.previewImage({
        urls: [getImageURL(currentMessage.image)] // 需要预览的图片http链接列表
      })
    }
  }

  getTypeTitle(type) {
    const { trafficTypes } = this.state;
    let matchTitle = (trafficTypes || []).find((item) => item.value === type);
    return matchTitle?.text || "";
  }
  formatTitle(msg) {
    let str = "";
    switch (msg.type) {
      case 8:
        str = "语音路况";
        break;
      case 5:
        str = "图片路况";
        break;
      case 1:
      case 2:
      case 3:
      case 4:
      case 6:
      case 7:
        str = this.getTypeTitle(msg.type);
        break;
    }
    return str;
  }
  getTypeIcon(type) {
    const { trafficTypes } = this.state;
    let matchTitle = (trafficTypes || []).find((item) => item.value === type);
    return matchTitle?.icon || "";
  }
  formatImage(msg) {
    let key = "";
    switch (msg.type) {
      case 8:
        key = "FmRJoGUQeuIBTE9AP2W3DPsT-urt"; // 语音图标
        break;
      case 5:
        key = msg.image;
        break;
      case 1:
      case 2:
      case 3:
      case 4:
      case 6:
      case 7:
        key = this.getTypeIcon(msg.type);
        break;
    }
    return getImageURL(key);
  }

  render () {
    let { showPop, currentMessage, playStatus } = this.props;
    // let { playStatus } = this.state;

    return (
      <Popup
        className="marker-popup"
        show={showPop}
        position="bottom"
        zIndex={2025}
        closeable
        onClose={this.handleMarkerPopupClose.bind(this)}
      >
        {!!currentMessage && (
          <View className="popup-content">
            {/* 图片地址 */}
            <Image
              src={this.formatImage(currentMessage)}
              className="left-image"
              mode="aspectFit"
              onClick={this.handleImageClick.bind(this)}
            />
            <View className="right-content">
              <View className="content-top">
                <View className="title">{this.formatTitle(currentMessage)}</View>
                <View className="desc">
                  来自
                  <Text className="name">{currentMessage?.sender?.name ? currentMessage.sender.name : '车友'}</Text>
                </View>
                <View className="time">{formatRelativeTime(currentMessage?.t)}</View>
              </View>
              <View className="content-bottom">
                {currentMessage.type == 8 && <View className="popup-voice-msg">
                  <View
                    className={`voice-msg-item ${styles.messageItem} ${styles.right}`}
                  >
                    <View className={styles.body}>
                      <View
                        className={`${styles.content} ${styles.text} ${
                          playStatus ? styles.playing : ""
                        }`}
                        onClick={this.handlePlayAudio.bind(
                          this,
                          currentMessage?.audio
                        )}
                      >
                        {/* <Text content={`语音${props.message.speech}`} /> */}
                        <Audio className="voice-msg-item-audio" speech={currentMessage?.speech} />
                      </View>
                    </View>
                  </View>
                </View>}
                {currentMessage.type != 8 && <View className="address-detail">{currentMessage?.road?.name}</View>}
                <View className="directions">{currentMessage?.road?.direction}</View>
                {!!currentMessage?.text && <View className="remark">{currentMessage?.text}</View>}
              </View>
            </View>
          </View>
        )}
        {/* <View className="popup-btns">
          <View className="popup-btn cancel">去这里</View>
          <View className="popup-btn" onClick={this.toReportPage.bind(this)}>路况上报</View>
        </View> */}
      </Popup>
    )
  }
}
