.navbar2 {
  background: transparent !important;
}
.navbar {
  position: fixed;
  z-index: 4;
  top: 0;
  width: 100%;
  background-color: #FF5925;
  box-sizing: border-box;
  color: #000;
  overflow: hidden;

  &::after {
    border: none;
  }
  .title {
    color: #000;
    // color: #fff;
  }

  
  
}

page {
  height: 100%;
}

.index {
  background-repeat: no-repeat;
  background-size: contain;
  background-color: #f2f4f5;
}