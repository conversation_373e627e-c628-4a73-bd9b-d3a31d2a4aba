// 友盟统计导入注意事项：在app.jsx文件中 uma模块的导入一定要在Taro的导入之前，如eslint出现提示，建议关掉app.jsx文件的相关eslint检查，一般是关于import前置 或 绝对路径模块要先于相对模块的eslint提示。
// 把uma 添加到Taro，后续通过Taro.uma调用uma方法 ,es6模块导入的是模块引用，
// 因此放心注入，后面页面导入Taro模块是可以获取uma的
// let uma:{
//   init?: any
// } = {};
// if (process.env.TARO_ENV === 'weapp') {
//   uma = require('umtrack-wx');
//   uma.init({
//     appKey: '62848aff30a4f67780e0e409',
//     useOpenid: false,// 是否使用openid进行统计，此项为false时将使用友盟+随机ID进行用户统计。使用openid来统计微信小程序的用户，会使统计的指标更为准确，对系统准确性要求高的应用推荐使用OpenID。
//     autoGetOpenid: false,// 是否需要通过友盟后台获取openid，如若需要，请到友盟后台设置appId及secret
//     // debug: true, //是否打开调试模式
//   });
// }
// if (process.env.TARO_ENV === 'alipay') {

// }

import uma from "./uma";

import Taro, { getCurrentInstance } from "@tarojs/taro";
import React, { Component } from "react";
// import Index from './pages/index'
import "taro-ui/dist/style/index.scss";
import "./app.scss";

import { getUserInfo } from "./api/modules/user";
import { setLoggedIn, setUserInfo, getLoggedIn } from "./gdata";
import { requestAuth } from "./api/modules/auth";
import { pushWebView, showModal, showToast } from "@/tools";
import { setUserType, setOpenAppFlag, getOpenAppFlag } from "@/gdata";
import { UserType, ThirdPartBiz } from "@/enums";
// import { BizPages } from '@/env/pages';
import * as Pages from "@/env/pages";
// import * as Pages from '@/env/pages';

Taro.uma = uma;
// eslint-disable-next-line no-undef
// const miniShopPlugin = requirePlugin('mini-shop-plugin');
enum LifeCycle {
  Mounted = "mounted",
  Show = "show",
}

let isReady = false;
class App extends Component<any, any> {
  // onLaunch() {
  //   miniShopPlugin.initApp(this, wx);
  // }
  $instance = getCurrentInstance();
  router: any = this.$instance.router;
  onLaunch(options) {
    if (process.env.NODE_ENV == "development") {
      showToast("测试版");
    }
    if (Taro.canIUse("getUpdateManager")) {
      const updateManager = Taro.getUpdateManager();
      if (updateManager) {
        updateManager.onCheckForUpdate(function (res) {
          // 请求完新版本信息的回调
          console.log(res.hasUpdate);
        });
        updateManager.onUpdateReady(function () {
          Taro.showModal({
            title: "更新提示",
            content: "新版本已经准备好，是否重启应用？",
            success: function (res) {
              if (res.confirm) {
                // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                updateManager.applyUpdate();
              }
            },
          });
        });
        updateManager.onUpdateFailed(function () {
          // 新的版本下载失败
          Taro.showModal({
            title: "更新提示",
            content: "新版本下载失败，请您删除当前小程序，重新搜索打开。",
          });
        });
      }
    } else {
      console.log("新版本下载失败");
    }
    this.initOpenAppStatus(options);
    requestAuth() /* .catch(() => {
      return requestAuth(true)
    }) */
      .then(getUserInfo)
      .then((info) => {
        setUserInfo(info);
        setLoggedIn(true);
        isReady = true;
        this.initThirdAppConfig(LifeCycle.Mounted);
      })
      .catch((err) => {
        isReady = true;
        console.error(err);
        setLoggedIn(false);
        this.initThirdAppConfig(LifeCycle.Mounted);
      }); /* .finally(() => {
      this.initThirdAppConfig(LifeCycle.Mounted);
      isReady = true;
    }) */
  }
  initOpenAppStatus(params) {
    // console.log(params);
    if (!params) return;
    const scene = params.scene;
    const openableSences = [
      // 1036, // 通过场景值1036（从app分享卡片）打开的小程序，不再可以打开app
      1069, // 从 APP 打开
    ];
    const openableSences2 = [
      1038, // 从其他小程序返回小程序
      1089, // 小程序从聊天顶部场景
      1090, // 长按小程序右上角菜单唤出最近使用历史
    ];
    const couldOpenAppInLastTime = getOpenAppFlag();
    let couldOpenApp = openableSences.some((item) => scene == item);
    // debugger
    // openableSences2场景打开时 继承上次通过 openableSences 打开app的能力
    if (!couldOpenApp && couldOpenAppInLastTime) {
      couldOpenApp = openableSences2.some((item) => scene == item);
    }
    setOpenAppFlag(couldOpenApp);
  }
  componentDidShow(options) {
    console.log("appts: ", options);

    // 为避免重复执行
    if (isReady) {
      this.initThirdAppConfig(LifeCycle.Show);
      this.initOpenAppStatus(options);
    }
  }

  componentDidHide() {}

  componentDidCatchError() {}
  initThirdAppConfig(lifeCycle: string) {
    const options = Taro.getLaunchOptionsSync();
    // 车主卡小程序appid: wxb89a89913dc530d1
    const currentPages = Taro.getCurrentPages();
    if (currentPages.length > 1) return;
    // 从其他小程序跳转而来: 需要每次进入都执行以下跳转逻辑
    if (
      +options.scene === 1037 &&
      options.referrerInfo &&
      options.referrerInfo.extraData &&
      options.referrerInfo.extraData.type
    ) {
      // showModal(`来自：${options.referrerInfo.appId}`, JSON.stringify(options.referrerInfo.extraData, null, 4));
      const { type, page, userPhone } = options.referrerInfo.extraData;
      if (type == ThirdPartBiz.CheZhuKaVip) {
        setUserType(UserType.CheZhuKa);
        if (page === Pages.BizPages.CAR_WASH) {
          pushWebView(Pages.CAR_WASH_SHOP_LIST, { replace: false });
        } else if (page === Pages.BizPages.CAR_WASH_ORDERS) {
          pushWebView(Pages.CAR_WASH_ORDER_LIST, {
            requireSignIn: true,
            replace: false,
          });
        }
      }
    } else {
      // 只要不是从车主卡小程序跳转而来，用户类型重置为jglh
      setUserType(UserType.Jglh);

      // 分享的webview通过首页中转进入webview，解决直接进入二级页面无法回到首页问题
      // TODO: 多次点击不同分享页面的处理
      // if (options.path != 'pages/web/index') {
      //   const url = options.query.url;
      //   if (url) {
      //     pushWebView(decodeURIComponent(url));
      //   }
      // }
    }
  }
  render() {
    return this.props.children;
  }
}
export default App;
