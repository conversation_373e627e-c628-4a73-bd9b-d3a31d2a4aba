import Taro from "@tarojs/taro";
import { UserType } from "@/enums";

enum Keys {
  LoggedIn = "loggedIn",
  UserInfo = "user",
  UserType = "userType",
  SessionId = "sessionId",
  NextURL = "nextURL",
  OpenAppFlag = "openAppFlag",
  LoginCount = "LoginCount",
}

const globalData = {
  // 是否能打开App
  [Keys.OpenAppFlag]: false,

  // 是否已登录
  [Keys.LoggedIn]: false,

  // 用户资料信息
  [Keys.UserInfo]: {},

  // 用户类型
  [Keys.UserType]: UserType.Jglh,

  // 用户sessionId
  [Keys.SessionId]: Taro.getStorageSync("sessionId"),

  // 登录次数。由于微信小程序对h5与小程序通信的限制不得不采用此方法：每次登录成功都为登录计数+1，webview页面通过对比此数值是否发生变化决定是否需要重新加载页面，更新页面token
  [Keys.LoginCount]: 0,
};

export function set(key: Keys, value: any) {
  console.log("set ", key, " as ", value);
  globalData[key] = value;
  Taro.setStorageSync("gdata", JSON.stringify(globalData));
  // console.log(globalData);
}

export function get(key: Keys) {
  const gdata = Taro.getStorageSync("gdata");
  if (gdata) {
    const parsedData = JSON.parse(gdata);
    return parsedData[key] || globalData[key];
  }
  return globalData[key];
}

/**
 * 设置登录状态
 * @param value
 */
export function setLoggedIn(value: boolean) {
  set(Keys.LoggedIn, value);
}

export function getLoggedIn() {
  return get(Keys.LoggedIn);
}

export function addLoginCount() {
  set(Keys.LoginCount, getLoginCount() + 1);
}

export function isAuthStateChanged(value: number) {
  return getLoginCount() !== value;
}

export function getLoginCount() {
  return get(Keys.LoginCount);
}

export function setOpenAppFlag(value: boolean) {
  // set(Keys.OpenAppFlag, value);
  Taro.setStorageSync(Keys.OpenAppFlag, value);
}

export function getOpenAppFlag() {
  return Taro.getStorageSync(Keys.OpenAppFlag);
  // return get(Keys.OpenAppFlag);
}

export function setUserInfo(value: object) {
  set(Keys.UserInfo, value);
}

export function getUserInfo() {
  return get(Keys.UserInfo);
}

export function setUserType(value: UserType) {
  set(Keys.UserType, value);
}

export function getUserType(): UserType {
  return get(Keys.UserType);
}

export function setWebViewCallbackURL(value: string) {
  set(Keys.NextURL, value);
}

export function getWebViewCallbackURL(): string {
  return get(Keys.NextURL);
}

export function setSessionId(value: string) {
  set(Keys.SessionId, value);
  Taro.setStorageSync("sessionId", value);
}

export function getSessionId() {
  return get(Keys.SessionId);
}
