import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { AtTabBar } from 'taro-ui'

interface Props {}
interface Item {
  max?: number
  dot?: boolean
  text?: string
  title: string
  iconPrefixClass?: string
  iconType?: string
  selectedIconType?: string
  image?: string
  selectedImage?: string
  path: string
}
interface State {
  currentIndex: number,
  TabBarConfig: {
    borderStyle: string,
    custom: boolean,
    list: Item[],
    selectedColor: string
  }
}

let app = Taro.getApp();

export default class Index extends Component<Props, State> {
  // config: Config = {
  //   navigationBarTitleText: 'tabbar'
  // }

  tabbarView:any;

  constructor(props) {
    super(props)
    let list: Item[] = [];
    app.config.tabBar.list.forEach((item: any) => {
      list.push({
        title: item.text,
        image: app.tabBarImage[item.iconPath],
        selectedImage: app.tabBarImage[item.selectedIconPath],
        path: `/${item.pagePath}`
      });
    });


    this.state = {
      currentIndex: 0,
      TabBarConfig: {
        borderStyle: app.config.tabBar.borderStyle,
        custom: app.config.tabBar.custom,
        list: list,
        selectedColor: app.config.tabBar.selectedColor
      }
    }
  }

  componentWillMount () {
   }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidShow () {

  }

  componentDidHide () {


  }

  componentDidUpdate() {
    // app.isFirst = false;
    console.log('222');
  }

  handleClick(ev) {
    let { TabBarConfig } = this.state;
    let list = TabBarConfig.list;
    Taro.switchTab({
      url: list[ev].path
    });

    // this.setState({
    //   currentIndex: ev
    // });

  }

  getComponentInfo() {
    console.log(this.tabbarView, '222222');
  }

  render () {
    let { currentIndex, TabBarConfig } = this.state;
    return (
      <AtTabBar
        ref={(ref) => {
          this.tabbarView = ref;
        }}
        className="tabbar"
        tabList={TabBarConfig.list}
        onClick={this.handleClick.bind(this)}
        current={currentIndex}
      />
    )
  }
}
