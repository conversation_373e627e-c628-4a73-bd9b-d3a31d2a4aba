.active-list {
  font-size: 0;
  background: #f2f4f5;
  overflow: hidden;
  // padding: 0 16px;
  padding-bottom: 20px;
  .active-title {
    // background: #fff;
    height: 86px;
    line-height: 86px;
    text-align: center;
    // margin: 5px;
    // padding: 0 32px;
    // border-bottom: 1px solid #ededed;

    .active-img {
      // background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAMAAAAM7l6QAAACEFBMVEVMaXH/ODP/NzP/OTP/NTP/NTP/NTP/NTP/NjP/NTP/NjP/NjP/PDP/QDP/PjP/PTP/OjP/QDP/PDP/PTP/OjP/OjP/NzP/PjP/OjP/NzP/////YzT/ZDT/YTT/XzT/XTT/WzT/PDP/OTP/OjP/ODP/WTT/NzP/PTP/QTP/NjP/QzP/RTP/SDT/RjT/PjP/QDP/VzT/TDT/VTT/UzT/UTT/UDT/TjT/SjT/NTP/7er/+/r/9PP/T0D/xcD/WjX/cFL/qpj/b0//1dL/3Nr/r6v/5OL/gWX/6Ob/dlf/aUr/WDX/qJb/f2X/Wzb/q5j/8e3/moX/7+z/9/b/yLz/19T/yMT/1s7/xbr/QTf/kXz/STn/W0v/3db/t7H/z8b/s6b/5eD/Qzj/fG7/lI7/v7X/n5L/8/H/iHn/inr/joD/rqP/lYT/qKD/o5T/bF7/jHz/ysf/m5T/nYz/mZD/iXz/2tj/ycL/+vn/+Pb/5eP/q53/eWn/5eT/wrj/TUT/VUv/qaX/29n/YU3/1ND//f3/VUn/y8f/6Of/3dj/cWX/vLb/UEH/RDf/49//bVz/4t7/aFn/fGv/Xk3/1NH/yMP/Rzb/qaL/0M3/Z1X/6ef/r6P/7+3/1c7/cF3/VUD/nI7/aVX/8vH/2dT/gG//pZ3/0c3/oJX//v7/f3H/8fD/p5r/6uj/gnP/Tjb/TD3/cV4FT11jAAAAGnRSTlMAigNVW9+s+tsGU1Rb8tms5PlcqwcGlODjk1bN5KgAAAGBSURBVBgZdcEFVxRhAAXQR4eBYj8L1HXHmZ0NUsog7G4xMBAVu0HBRkxswVYQA+svOu9Dzspx514A03xBpvqCTPcFmeELMtMXJM8XJCDr1wRWL1uyeEHgX5ColG9ct5Zk2aKFK6LLV66KGpACKduylYMutJDbCwxIoezc08C404UGpEh2Nx1j3MUiA1IsjTzCuPZiA1IizTxB483A+1cPb5cYkJgc4CF6Hrz9+bvvQ+xxzICUymEePH/pete7H99J9vaUGpCQ7OPZkOfrF377TH58EhKIK8d5xnXd5y/JfpfkXVcgjuzgUcdxHvF11Sen6ll5hSMQW+q5y7btO3xR+cuuvHeDtkAsY8Nmy7I6SHZbJFstgYyKSN2miOcW+fQ+yVMRz1jI6LBULw17rnTy5jXy5N6wZxJkXFBqa4Jy+Wpbxbn924KSAxmZ7yMXMmHy7ITGjIeRMiuhFPyVNCeBJAxJnjL3PxOTEZedOW+YzBEYJis1PS1jvpGRlp6ahUF/ADxmjYBAOj4DAAAAAElFTkSuQmCC);
      // background-repeat: no-repeat;
      // background-size: 35px 35px;
      // background-position-x: 0;
      // background-position-y: center;
      // font-size: 32px;
      font-size: 32px;
      font-weight: 800;
      color: #333333;
      // padding-left: 40px;
    }


    .more {
      float: right;
      font-size: 20px;
      color: #808080;

      .img {
        width: 25px;
        height: 25px;
        margin-left: 13px;
        vertical-align: middle;
      }
    }
  }

  .w{
    // display: flex;
    // flex-wrap: wrap;
    // justify-content: space-between;
    display: grid;
    grid-template-columns: 1fr 1fr;
    // grid-template-rows: 150px 150px;
    gap: 30px;

  .list-item {
    // width: 50%;
    // margin-bottom: 18px;
    display: inline-block;
    text-align: center;
    // padding: 0 8px;
    box-sizing: border-box;

    .item {
      background: #fff;
      border-radius: 20px;
      padding-bottom: 16px;
      overflow: hidden;
      // width: 330px;

      .img-w {
        background: #fff;
        height: 330px;

        .img {
          width: 100%;
          height: 100%;
        }
      }

      .title {
        display: block;
        font-size: 26px;
        max-height: 68px;
        // height: 52px;
        text-align: left;
        padding: 0 16px;
        margin-top: 12px;
        font-weight: bold;
        visibility: visible !important;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        word-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        /*! autoprefixer: off */
      }
      .time {
        display: block;
        text-align: left;
        padding: 0 16px;
        padding-top: 10px;
        font-size: 24px;
        color: #ccc;
      }
    }

  }
}
  .list-item1 {
    background: #fff;
    // padding: 24px 32px 26px 29px;
    padding: 24px 0 26px 0;
    margin: 0 20px 0 20px;
    overflow: hidden;
    border-bottom: 1px solid #f0f0f0;
    .img {
      width: 231px;
      height: 165px;
      margin-right: 23px;
      float: left;
    }

    .title {
      display: block;
      font-size: 31px;
      min-height: 62px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      word-break: break-all;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }

    .time {
      display: block;
      font-size: 22px;
      color: #808080;
      margin-top: 34px;
    }
  }

  .more-go {
    text-align: center;
    margin-bottom: 10px;
    .more-text {
      font-size: 30px;
      color: rgb(123, 123, 248);
    }
  }
  .empty-tip {
    min-height: 80px;
    font-size: 32px;
    text-align: center;
    padding-top: 30px;
    color: rgb(170, 170, 170);
  }
}
