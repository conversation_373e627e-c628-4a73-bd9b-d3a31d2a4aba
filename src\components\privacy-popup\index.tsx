import Taro, {
  getEnv,
  useRouter,
} from "@tarojs/taro";
import { Button, View } from "@tarojs/components";
import { Popup } from "@antmjs/vantui";
import React, { useCallback, useEffect, useState } from "react";
import "./index.scss";

// import { login } from "@/tools";
// import { getLoggedIn } from "@/gdata";

const PrivacyPopup: React.FC<{
  showPop: boolean;
  authorize?: (value: boolean) => void;
}> = (props) => {
  const env = getEnv();
  const router = useRouter();

  const [show, setShow] = useState(false);

  // 判断是否是 TabBar 页面
  const isTabBarPage = useCallback(() => {
    const currentPages = Taro.getCurrentPages();
    if (currentPages.length > 0) {
      const currentPage = currentPages[currentPages.length - 1];
      const tabBarList = [
        {
          pagePath: 'pages/index/index',
          text: '首页',
        },
        {
          pagePath: 'pages/shop/index',
          text: '商城',
        },
        {
          pagePath: 'pages/live/live',
          text: '互动',
        },
        {
          pagePath: 'pages/user/index',
          text: '我的',
        },
      ]

      if (tabBarList && tabBarList) {
        return tabBarList.some(tabItem => tabItem.pagePath === `${currentPage.route}`);
      }
    }
    return false;
  }, [])

  const checkNeedAuthorization = useCallback(() => {
    // 关于同时会出现官方隐私弹窗和自定义隐私弹窗问题, 改成先注册 wx.onNeedPrivacyAuthorization 再调用隐私接口
    if (wx.onNeedPrivacyAuthorization) {
      wx.onNeedPrivacyAuthorization(resolve => {
        // console.log('onNeedPrivacyAuthorization', resolve)
      })
    }
    if (Taro.getEnv() === Taro.ENV_TYPE.WEAPP && wx.getPrivacySetting) {
      wx.getPrivacySetting({
        success: res => {
          if (res.needAuthorization) {
            // 需要弹出隐私协议
            setShow(true)
            if (isTabBarPage()) {
              Taro.hideTabBar()
            }
          } else {
            // 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口
            props.authorize && props.authorize(true);
          }
        },
        fail: () => {},
        complete: () => {}
      })
    } else {
      props.authorize && props.authorize(true);
    }
  }, [isTabBarPage, props]);

  // 初始化
  // useEffect(() => {
  //   // const { roomid } = router.params;
  //   checkNeedAuthorization()
  // }, []);

  // 检测showPop变化
  useEffect(() => {
    if (props.showPop) {
      checkNeedAuthorization()
    }
  }, [checkNeedAuthorization, props.showPop]);

  const handleAgree = useCallback(() => {
    if (isTabBarPage()) {
      Taro.showTabBar()
    }
    setShow(false)
    props.authorize && props.authorize(true);
  }, [isTabBarPage, props]);

  const handleDisagree = useCallback(() => {
    setShow(false)
    props.authorize && props.authorize(false);
    // Taro.navigateBack();
  }, [props]);

  const openPrivacyContract = useCallback(() => {
    wx.openPrivacyContract({
      success: (res) => {
        console.log("openPrivacyContract success");
      },
      fail: (res) => {
        console.error("openPrivacyContract fail", res);
      },
    });
  }, []);

  return (
    <React.StrictMode>
      <Popup round closeOnClickOverlay={false} show={show} position="bottom" onClose={() => setShow(false)}>
        <View className="dialog__hd">
          <text className="dialog__title">
            用户隐私保护提示
          </text>
        </View>
        <View className="dialog__bd">
          <View className="dialog__tips">
            为保障您的合法权益，在您使用【交广领航】服务之前，请仔细阅读
            <View
              className="dialog__tips_color"
              onClick={openPrivacyContract}
            >
              《交广领航小程序隐私保护指引》
            </View>
            并确认了解我们对您的个人信息处理原则。
          </View>
          <View className="dialog__tips">
            如您同意《交广领航隐私保护指引》，请点击“同意并继续”后使用我们的产品和服务，我们依法全力保护您的个人信息安全。
          </View>
          <View className="dialog__tips">
            若点击“不同意并退出”，您将无法使用我们的产品和服务，并会退出小程序。
          </View>
        </View>
        <View className="dialog__ft">
          <Button
            id="agree-btn"
            type="default"
            // @ts-ignore
            openType="agreePrivacyAuthorization"
            onAgreePrivacyAuthorization={handleAgree}
          >
            同意并继续
          </Button>
          <View
            id="disagree-btn"
            onClick={handleDisagree}
          >
            不同意，退出
          </View>
        </View>
      </Popup>
    </React.StrictMode>
  );
};
export default PrivacyPopup
