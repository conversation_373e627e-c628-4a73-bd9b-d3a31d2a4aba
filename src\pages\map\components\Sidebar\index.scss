.sidebar {
  position: fixed;
  right: 20px;
  top: 20%;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  // gap: 23px;
  background: #fff;
  padding: 12px 4px;
  border-radius: 4px;
  font-size: 0;
  line-height: 1;

  .sidebar-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    // background: rgba(255, 255, 255, 0.95);
    // border-radius: 8px;
    // box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    box-sizing: border-box;
    margin-bottom: 24px;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      bottom: -13px; // 24px margin-bottom 的一半，让分隔线居中
      left: 50%;
      transform: translateX(-50%);
      width: 60%; // 不要占满整个宽度，看起来更美观
      height: 1px;
      border-radius: 1px;
      background: #d4d4d4;
    }
    &:last-child {
      margin-bottom: 0;
      &::after {
        display: none;
      }
    }
    // &:active {
    //   transform: scale(0.95);
    // }

    // &.active {
    //   background: #fd4925;
    //   border-color: #fd4925;
    //   box-shadow: 0 4px 12px rgba(253, 73, 37, 0.3);
    // }

    .sidebar-icon {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}
