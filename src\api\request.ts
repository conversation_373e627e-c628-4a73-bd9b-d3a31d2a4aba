import Taro from "@tarojs/taro";
import { getSessionId } from "@/gdata";

const isWeapp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP;
const isAlipay = Taro.getEnv() === Taro.ENV_TYPE.ALIPAY;
/**
 * 处理请求地址，对必要的接口追加v=394参数
 * @param {*} url
 */
function handleRequestURL(url) {
  try {
    // 统一增加v=458参数,路径集合
    const include458 = ["/Radio/vip/ads/infos"];
    const exclude440 = ["/Radio/vip/ads/infos"];
    // 请求统一增加v=440参数
    const shouleAddVersion440 = !exclude440.some(
      (item) => url.indexOf(item) > -1
    );
    const shouleAddVersion458 = include458.some(
      (item) => url.indexOf(item) > -1
    );
    let version = 440;
    if (shouleAddVersion458) {
      version = 458;
    } else if (shouleAddVersion440) {
      version = 460;
    } else {
      return url;
    }
    return url.indexOf("?") >= 0
      ? `${url}&v=${version}`
      : `${url}?v=${version}`;
  } catch (err) {
    return url;
  }
}
const baseOptions = (params, method = "GET") => {
  let { url, data } = params;
  let contentType = "application/x-www-form-urlencoded";
  contentType = params.contentType || contentType;
  // 获取token
  // const token = Taro.getStorageSync('sessionId');
  const token = getSessionId();

  const option: any = {
    isShowLoading: false,
    loadingText: "正在加载",
    url: handleRequestURL(url),
    data: data,
    method: method,
    header: isWeapp
      ? { "content-type": contentType, XCXTOKEN: token, platform: "XCX" }
      : { "content-type": contentType, "ALIPAY-XCXTOKEN": token },
  };
  return Taro.request(option)
    .then((res) => {
      if (!res.data) {
        // 不存在data数据
        return Promise.reject("数据异常!");
      }

      if (200 == res.data.code) {
        return Promise.resolve(res.data.data);
      }

      // 判断code状态
      if (600 == res.data.code) {
        Taro.navigateTo({
          url: "/pages/login/index",
        });
        return new Promise(() => {});
      }

      return Promise.reject(res.data.msg);
    })
    .catch((err) => {
      if (err == "INVALID") {
        Taro.navigateTo({
          url: "/pages/login/index",
        });

        return new Promise(() => {});
      }
      return Promise.reject(err);
    });
};

export const doGet = (url, data: any = "") => {
  let option = { url, data };
  return baseOptions(option, "GET");
};

export const doPost = (url, data: any = "", contentType?) => {
  let params = { url, data, contentType };
  return baseOptions(params, "POST");
};

export const doPostJson = (url, data: any = "") => {
  return doPost(url, data, "application/json");
};
