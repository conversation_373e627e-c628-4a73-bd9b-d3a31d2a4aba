.float-btn {
  position: fixed;
  bottom: 20px;
  // left: 50%;
  // transform: translateX(-50%);
  right: 20px;
  border-radius: 50%;
  background: #1E9EF5;
  background: linear-gradient(180deg, #32B7FF, #1C9CF5);
  box-shadow: 0 0 15px 0 rgba(28, 155, 245, 0.3);
  transition: all 250ms;
  color: white;
  display: flex;
  flex-direction: column;
  padding: 20px;
  .btn-openapp {
    background: transparent;
    line-height: 1.1;
    font-size: 28px;
    display: flex;
    flex-direction: column;
    &::after {
      border: 0;
    }
    color: white;
    padding: 0;
    margin: 0;
  }
  &.float-btn-hover {
    opacity: 0.8
  }
}
@keyframes myBreath{
  0%{
      transform: scale(0.8);
  }
  50%{
      transform: scale(1);
  }
  100%{
      transform: scale(0.8);
  }
}
.breathing-btn{
  position: fixed;
  right: 0;
  bottom: 30px;
  width: 90px;
  height: 90px;
  background: url(./images/signbg.png) no-repeat center;
  background-size: 100% 100%;
  transform:translateX(0px);
  transition: .5s linear;
  .btn-checkin {
    color:white;
    font-size: 22px;
    width: 100%;
    margin-top: 50px;
    display: inline-block;
    line-height: 40px;
    text-align: center;
    background: #FD4925;
    border-radius: 20px;
    animation:myBreath 1s linear infinite;
  }
}
// @keyframes mymove
//   {
//     from {right:0px;}
//     to {right:-45px;}
//   }
.breathingStart{
  opacity: 0.5;
  transform:translateX(30px);
  transition: .5s linear;
}
