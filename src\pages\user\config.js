const allOrderUrl = `${BASE_URL}/actions/app/mcar/?#/account/orders`; // 全部订单
// const orderUrl = `${BASE_URL}/actions/app/mcar/?#/account/orders`;
const bagUrl = `${BASE_URL}/actions/app/mcar/?#/account/cards?nohead`
const goldUrl = `${BASE_URL}/actions/app/mcar/?#/virtual/gold/record?nohead`
const ticketUrl2 = `${BASE_URL}/actions/app/mcar/?#/ticket/list?nohead`;

const orderArr = [
  // {
  //   name: '订单',
  //   class: 'order',
  //   url: orderUrl
  // },
  {
    name: '卡包',
    class: 'bag',
    url: bagUrl
  },
  {
    name: '金币',
    class: 'gold',
    url: goldUrl
  },
  {
    name: '优惠劵',
    class: 'ticket',
    url: ticketUrl2
  }
];

// const vipUrl = `${BASE_URL}/actions/app/mcar/?#/vip?` // 交广领航会员
// const actionUrl = `${BASE_URL}/actions/app/mcar/?#/wechat/event/orders?nohead` // 报名活动
// const fuelUrl = `${BASE_URL}/actions/app/mcar/?#/fuelcard/mycard?nohead` // 我的油卡
// const carUrl = `${BASE_URL}/actions/app/mcar/?#/account/car?nohead` // 爱车档案
// const ticketUrl = `${BASE_URL}/actions/app/mcar/?#/trips/orders?nohead` // 我的卡卷
// const washCardUrl = `${BASE_URL}/actions/app/mcar/?#/washcard/exchange?nohead` // 兑换洗车卡
const serviceUrl = `${BASE_URL}/actions/app/help` // 客服帮助
const feedbackUrl = `${BASE_URL}/actions/app/feedback/` // 意见反馈
// const list = [
//   {
//     name: '交广领航VIP',
//     icon: 'vip',
//     url: vipUrl
//   },
//   {
//     name: '我的报名活动',
//     icon: 'bm',
//     url: actionUrl
//   },
//   // {
//   //   name: '我的油卡',
//   //   icon: 'fuled',
//   //   url: fuelUrl
//   // },
//   {
//     name: '爱车档案',
//     icon: 'car',
//     url: carUrl
//   },
//   // {
//   //   name: '我的卡劵',
//   //   icon: 'card',
//   //   url: ticketUrl
//   // },
//   {
//     name: '兑换卡券',
//     icon: 'exchange',
//     url: washCardUrl
//   },
// ];

// const otherArray = [
//   {
//     name: "客服帮助",
//     image: "Fo1D2dIePRukoy91ik08tBuTCPy-",
//     url: serviceUrl
//   },
//   {
//     name: "扫一扫",
//     image: "Fr-gifb4S-n9qZkUIyFeehL35SL1",
//     url: ""
//   },
//   {
//     name: "分享好友",
//     image: "FroO3Q-LUHx7bQMWRqKN3fU4Ik8e",
//     url: ""
//   },
//   {
//     name: "退出登录",
//     image: "FnZlIJ6Cc49GRWlkZh2Tl9JDcsVD",
//     url: ""
//   }
// ];


export { orderArr, feedbackUrl, allOrderUrl, serviceUrl };
