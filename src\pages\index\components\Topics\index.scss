/* src/pages/layout/gridLayout/index.scss */
.topics-container {
  padding: 20px;
  display: grid;
  grid-template-rows: auto auto;
  gap: 20px;
  background: #ffffff;
  margin-bottom: 20px;
  border-radius: 20px;

  .top {
    display: grid;
    grid-template-columns: 1fr 1fr;
    // grid-template-rows: 1fr 1fr; /* 右侧上下两部分的高度相等 */
    gap: 20px;
  }

  .left {
    border-radius: 20px;
    overflow: hidden;
    padding-bottom: 120%; /* 保证宽高比为 1:1.2 */
    position: relative;
  }

  .right {
    display: flex;
    flex-direction: column; /* 让右侧元素上下排列 */
    justify-content: space-between; /* 保证上下元素之间的间距自动调整 */
  }

  .right-top,
  .right-bottom {
    max-width: 100%;
    flex: 1;
    flex-shrink: 0;
    overflow: hidden;
    .img {
      height: 100%;
    }
  }
  .right-top {
    margin-bottom: 20px;
  }

  .bottom {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr; /* 三个底部部分均分整行 */
    gap: 20px;
  }

  .bottom1,
  .bottom2,
  .bottom3 {
    padding-top: 62.5%;
    position: relative;
    .img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
  .img {
    width: 100%;
    border-radius: 20px;
    display: block;
  }
  .card-swiper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    transform: translateY(0);
    .img {
      width: 100%;
      height: 100%;
      border-radius: 20px;
      display: block;
    }
    .swiper-item_view {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 0 1px;
      display: block;
      overflow: hidden;
      transform: translateY(0);
    }
  }

  // 指示点样式
  .spot-pagination {
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 20px;
    width: 100%;
    z-index: 1;
    .spot-pagination-bullet {
      margin-right: 10px;
      width: 16px;
      height: 4px;
      background: #ffffff;
      opacity: 0.4;
      border-radius: 2px;
    }
    // 当前指示点样式
    .spot-pagination-bullet-active {
      opacity: 1;
    }
  }
}
