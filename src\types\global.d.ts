declare module "*.png";
declare module "*.gif";
declare module "*.jpg";
declare module "*.jpeg";
declare module "*.svg";
declare module "*.css";
declare module "*.less";
declare module "*.scss";
declare module "*.sass";
declare module "*.styl";

declare module '*.module.less' {
  const classes: { readonly [key: string]: string };
  export default classes;
}

// declare module '*.module.scss' {
//   export const style: any
//  }

declare namespace NodeJS {
  interface ProcessEnv {
    TARO_ENV: 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'quickapp' | 'qq' | 'jd'
  }
}
// @ts-ignore
declare const process: {
  env: {
    TARO_ENV: 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'quickapp' | 'qq' | 'jd';
    [key: string]: any;
  }
}
declare const BASE_URL: string
declare const WS_URL: string
declare const UPLOAD_QINIU_URL: string
declare const wx: any
declare const my: any // 支付宝小程序
