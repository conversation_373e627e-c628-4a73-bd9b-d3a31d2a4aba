import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { Button } from '@tarojs/components'

import './Timer.scss'

interface Props {

}

interface State {
  time: number
}

export default class Index extends Component<Props, State> {
  // config: Config = {
  //   navigationBarTitleText: '倒计时组件'
  // }
  constructor(props: Props) {
    super(props);

    this.state = {
      time: 60
    }
  }
  componentWillMount () { }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidShow () {
  }

  componentDidHide () { }

  getTimeNum():number {
    return this.state.time;
  }

  setTimeNum(num: number) {
    this.setState({
      time: num
    });
  }

  render () {
    return (
      <Button className="verify send" >已发送({this.state.time})</Button>
    )
  }
}
