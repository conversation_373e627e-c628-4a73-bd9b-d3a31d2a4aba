import Taro, { getCurrentInstance } from '@tarojs/taro'
import React, { Component }  from 'react'
import { WebView, View } from '@tarojs/components'
import { getWebViewURL } from '../../tools'
import * as Pages from '../../env/pages'
import { getLoggedIn, setLoggedIn, getLoginCount } from '@/gdata';

const EMPTY_PAGE = 'about:blank';

type PageState = {
  toUrl: string,
  originURL: string,
  clickCount: number,
  loginCount: number,
  loggedIn:  boolean,
  postData?: any,
  isWeapp?: boolean,
  isAlipay?: boolean,
};
export default class Index extends Component<any, PageState> {

  $instance: any = getCurrentInstance()
  isWeapp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP
  isAlipay = Taro.getEnv() === Taro.ENV_TYPE.ALIPAY
  constructor(props) {
    super(props);
    this.state = {
      toUrl: Pages.MALL_HOME,
      originURL: EMPTY_PAGE,
      clickCount: 0,
      loggedIn: getLoggedIn(),
      loginCount: getLoginCount(),
      postData: {},
    };
  }

  componentWillMount () {
  }

  componentDidMount () {
    this.initPage();
  }

  componentWillUnmount () { }
  componentDidShow() {
    const isLoggedIn = getLoggedIn();
    const loginCount = getLoginCount();
    setLoggedIn(isLoggedIn);
    // 刷新页面条件：
    // 1. 登录状态发生变化
    // 2. 登录状态可能发生变化（跳转到了登录页面后返回）
    const loginStateChanged = (!this.state.loggedIn && isLoggedIn) || (this.state.loggedIn && !isLoggedIn);
    const shouldRefreshPage = this.state.loginCount != loginCount || loginStateChanged;
    if (shouldRefreshPage) {
      this.setState({
        loggedIn: isLoggedIn,
        loginCount: loginCount,
      });
      this.refreshPage();
    }
  }
  componentDidHide () {
    this.setState({
      clickCount: 0
     });
   }

  // config: Config = {
  //   navigationBarTitleText: '商城'
  // }

   onShareAppMessage(res) {
    // const url = this.state.postData.link || res.webViewUrl || this.state.originURL;
    let path = '/pages/shop/index';
    const shareInfo = {
      path: path,
      title: '领航商城',
      imageUrl: null,
    };
    const shareData = this.state.postData;
    if (shareData) {
      if(shareData.title) {
        shareInfo.title = shareData.title;
      }
      // web页初始化时传递的分享信息链接link为-"http://www.jgrm.net/mobile/jglh.html"
      if(shareData.link && shareData.link.indexOf('mobile/jglh.html') == -1) {
        shareInfo.path = '/pages/shop/index?url=' + encodeURIComponent(shareData.link);
      }
      if(shareData.imgUrl) {
        shareInfo.imageUrl = shareData.imgUrl;
      }
    }
    const promise = new Promise(resolve => {
      setTimeout(() => {
        const shareData = this.state.postData;
        if (shareData) {
          if(shareData.title) {
            shareInfo.title = shareData.title;
          }
          // web页初始化时传递的分享信息链接link为-"http://www.jgrm.net/mobile/jglh.html"
          if(shareData.link && shareData.link.indexOf('mobile/jglh.html') == -1) {
            shareInfo.path = '/pages/shop/index?url=' + encodeURIComponent(shareData.link);
          }
          if(shareData.imgUrl) {
            shareInfo.imageUrl = shareData.imgUrl;
          }
        }
        console.info('shareInfo:', shareInfo)
        resolve(shareInfo)
      }, 10)
    })
    // showModal('提示', JSON.stringify(shareInfo) + JSON.stringify(shareData))
    return {
      ...shareInfo,
      promise
    };
    // return {
    //   title: '领航商城',
    //   path: '/pages/shop/index'
    // }
  }

  onError(e) {
    console.error(e);
  }

  onLoad(e) {
    console.log(e);
  }

  onTabItemTap() {
    if (this.state.clickCount > 0) {
      this.refreshPage();
    }
    this.setState({
      clickCount: this.state.clickCount + 1
    });
  }
  // 刷新页面，有bug
  refreshPage() {
    console.log('refresh page')
    this.setState({
      toUrl: EMPTY_PAGE,
    });
    setTimeout(() => {
      this.initPage()
    }, 150)
  }
  initPage() {
    // 初始化分享参数
    let router = this.$instance.router
    let url = router.params.url ? decodeURIComponent(router.params.url) : '';
    const webviewURL = url ? getWebViewURL(url) : getWebViewURL(Pages.MALL_HOME);
    this.setState({
      toUrl: webviewURL,
      originURL: getWebViewURL(url, false), // 原始地址，不带授权参数
    });
  }
  onWebViewMessage(e) {
    let data = e.detail?.data;
    if (Array.isArray(e.detail.data)) {
      data = e.detail?.data?.slice(-1)[0];
    }
    // console.log('onWebViewMessage: ', data, e.detail.data);
    if (this.isAlipay && data.appid) {
      // 支付宝小程序，跳转其他小程序
      Taro.navigateToMiniProgram({
        ...data,
        // envVersion: 'develop',
        success: function(res) {
          // 打开成功
        }
      })
      return;
    }
    this.setState({
      postData: data,
    })
  }

  render () {
    // console.log(this.state.toUrl, '55');
    return (
      this.isAlipay ? (this.state.toUrl === EMPTY_PAGE ?
      <View className="loading" /> :
      <WebView
        onError={this.onError.bind(this)}
        onLoad={this.onLoad.bind(this)}
        onMessage={this.onWebViewMessage.bind(this)}
        src={this.state.toUrl}
      />) : <WebView
        onError={this.onError.bind(this)}
        onLoad={this.onLoad.bind(this)}
        onMessage={this.onWebViewMessage.bind(this)}
        src={this.state.toUrl}
      />
    )
  }
}
