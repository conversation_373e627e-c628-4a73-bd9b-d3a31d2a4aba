import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { View } from '@tarojs/components'
import { getPageURL, pushWebView } from '../../tools'
import { getURLSearchParams } from '@/tools'

interface Props { }

interface State {}

export default class Index extends Component<Props, State> {

  // config: Config = {
  //   navigationBarTitleText: '美容商家'
  // }

  constructor(props: Props) {
    super(props);
    this.state = {};
  }

  componentWillMount () {
    this.initPage();
  }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidHide () { }

  onError(e) {
    console.error(e);
  }

  onLoad(e) {
    console.log(e);
  }

  initPage() {
    const options = Taro.getLaunchOptionsSync();
    // console.log(options);
    // 兼容 车生活商家后台，商家门店小程序二维码：
    // 旧：https://radio.jgrm.net/xcx/b?b={shopId}
    // 新：https://radio.jgrm.net/miniapp/jglh/shop?b={shopId}
    // 车生活商家后台：https://radio.jgrm.net/fe/car-business/#/shop/services > 汽车美容小程序二维码
    const q = decodeURIComponent(options.query.q);
    const shopId = getURLSearchParams(q).b;
    const url = getPageURL(`/actions/app/mcar?#/shop/${shopId}/wash`);
    pushWebView(url, { replace: true });
  }

  render () {
    return (
      <View className="loading">加载中...</View>
    )
  }
}
