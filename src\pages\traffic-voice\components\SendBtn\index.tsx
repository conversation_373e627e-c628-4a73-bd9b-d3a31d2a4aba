import Taro from "@tarojs/taro";
import React, { Component } from "react";
import { View, Image, Text } from "@tarojs/components";
import { getImageURL } from "@/tools";
import { initQiniuUploadToken } from "@/api/modules/chat";

import "./index.scss";

interface Props {
  type: "voice" | "voiceSend" | "send";
  setVoiceMsg?: any;
  reportSubmit?: any;
}

type QiNiuResponse = {
  key: string;
  hash: string;
};
interface State {
  current: number;
  seconds: number;
  btnType: string;
  voiceLock: boolean;
  audioUploadConfig: any;
  recorderManager: any;
}
export default class Index extends Component<Props, State> {
  timerRef: any;
  touch: any;
  recorderManager: any;

  constructor(props) {
    super(props);
    this.timerRef = null; // 记录定时器
    this.touch = { startPoint: {} }; // 记录触摸点初始坐标信息
    // this.recorderManager = null; // 录音管理器
    this.state = {
      current: 0,
      seconds: 10,
      btnType: props.type, // 底部按钮类型
      voiceLock: false,
      audioUploadConfig: {},
      recorderManager: null,
    };
  }

  componentDidMount() {
    let that = this;
    const { btnType } = this.state;
    if (btnType == "voice") {
      initQiniuUploadToken(2).then((res) => {
        that.setState({
          audioUploadConfig: res,
        });
      });
      // this.recorderManager = Taro.getRecorderManager();
      const recorderManager = Taro.getRecorderManager();
      recorderManager.onStart(() => {
        console.log("recorder start");
      });
      recorderManager.onError((res) => {});
      recorderManager.onStop((res) => {
        if (that.state.voiceLock) {
          if (res.duration < 1000) {
            Taro.showToast({
              title: "录音时间太短，请长按录音",
              icon: "none",
              duration: 2000,
            });
          } else {
            that.uploadAudio(res);
          }
        } else {
          // filePath = ''
          // duration = 0
        }
      });
      this.setState({
        recorderManager,
      });
    }
  }

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  toPage(url: string) {
    // this.props.close();
  }

  handleReport() {
    const { btnType } = this.state;
    const { reportSubmit } = this.props;
    reportSubmit && reportSubmit(btnType);
  }

  // 发送音频
  uploadAudio(audio) {
    const { audioUploadConfig } = this.state;
    const { setVoiceMsg } = this.props;
    Taro.showLoading({
      title: "请稍等...",
    });
    Taro.uploadFile({
      url: UPLOAD_QINIU_URL,
      filePath: audio.tempFilePath,
      name: "file",
      formData: { token: audioUploadConfig.token, key: audioUploadConfig.rid },
    })
      .then((r) => {
        Taro.hideLoading();
        const result: QiNiuResponse = JSON.parse(r.data);
        // console.log("上传成功++", result);
        if (result.key) {
          this.setState({
            btnType: "voiceSend",
          });
          setVoiceMsg &&
            setVoiceMsg({
              speech: Math.ceil(audio.duration / 1000),
              url: result.key,
            });
          initQiniuUploadToken(2).then((res) => {
            this.setState({
              audioUploadConfig: res,
            });
          });
        }
      })
      .catch((e) => {
        Taro.hideLoading();
      });
  }
  // 取消发送音频
  cancelVoice() {
    const { setVoiceMsg } = this.props;
    setVoiceMsg && setVoiceMsg(null);
    this.setState({
      btnType: "voice",
    });
  }

  handleRecordStart(e) {
    let event = e;
    let that = this;
    const { recorderManager } = this.state;
    this.timerRef = setTimeout(() => {
      const options: any = {
        duration: 60000,
        sampleRate: 44100,
        numberOfChannels: 1,
        encodeBitRate: 192000,
        format: "mp3",
        frameSize: 50,
      };
      // 长按时设置为可发送状态
      that.setState({
        voiceLock: true,
      });
      that.touch.startPoint = event.touches[0]; // 记录触摸点初始坐标信息
      recorderManager.start(options);
      Taro.vibrateLong();
      Taro.showToast({
        title: "正在录音,往上滑动取消录音",
        icon: "none",
        duration: 60000,
      });
    }, 500);
  }

  handleTouchMove(e) {
    let that = this;
    const { voiceLock } = this.state;
    if (
      Math.abs(
        e.touches[e.touches.length - 1].clientY - that.touch.startPoint.clientY
      ) > 100
    ) {
      // console.log("设置为不发送语音");
      Taro.showToast({
        title: "松开手指,取消录音",
        icon: "none",
        duration: 60000,
      });
      that.setState({
        voiceLock: false,
      });
    } else {
      if (!voiceLock) {
        Taro.showToast({
          title: "正在录音,往上滑动取消录音",
          icon: "none",
          duration: 60000,
        });
      }
      that.setState({
        voiceLock: true,
      });
    }
  }

  handleRecordEnd(e) {
    let that = this;
    const { recorderManager } = this.state;
    // 延时处理 防止录音未调起就触发停止
    setTimeout(() => {
      recorderManager.stop();
    }, 400);
    that.timerRef && clearTimeout(that.timerRef);
    Taro.hideToast();
  }

  render() {
    let { type } = this.props;
    let { btnType } = this.state;
    // , '?imageView2/0/format/jpg/imageslim'
    return (
      <View className="send-btn-wrap">
        {btnType == "voiceSend" && (
          <View className="back" onClick={this.cancelVoice.bind(this)}>
            <Image
              src={require("../../images/del.png")}
              mode="widthFix"
              lazyLoad
              className="btn-icon"
            />
          </View>
        )}
        {/* 录音 */}
        {btnType == "voice" && (
          <View
            className="btn-primary"
            onTouchStart={this.handleRecordStart.bind(this)}
            onTouchEnd={this.handleRecordEnd.bind(this)}
            onTouchMove={this.handleTouchMove.bind(this)}
          >
            <Image
              src={require("../../images/mic.png")}
              mode="widthFix"
              lazyLoad
              className="btn-icon"
            />
            <Text className="btn-text">按住说话</Text>
          </View>
        )}
        {/* 发送 */}
        {(btnType == "send" || btnType == "voiceSend") && (
          <View className="btn-primary" onClick={this.handleReport.bind(this)}>
            <Image
              src={require("../../images/send.png")}
              mode="widthFix"
              lazyLoad
              className="btn-icon"
            />
            <Text className="btn-text">发送</Text>
          </View>
        )}
        {/* {(btnType == "voice" || btnType == "voiceSend") && <View className="del" onClick={() => Taro.navigateBack()}>X</View>} */}
      </View>
    );
  }
}
