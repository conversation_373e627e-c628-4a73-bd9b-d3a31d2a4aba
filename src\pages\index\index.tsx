import { getLoggedIn, getOpenAppFlag } from "@/gdata";
import {
  getFullImagePath,
  login,
  pushWebView,
  showModal,
  showToast,
} from "@/tools";
import { Image, Swiper, SwiperItem, Text, View } from "@tarojs/components";
import { Search } from "@antmjs/vantui";
import Taro from "@tarojs/taro";
import React, { Component } from "react";
import * as Pages from "@/env/pages";
import {
  getActionList,
  getHomeData,
  getWeappHomeConfig,
  getHomeAdData,
  getTopicData,
} from "../../api/modules/home";
import { getRoomList } from "../../api/modules/live";
import IndexLayout from "../../components/layout/IndexLayout";
import ActionList from "./components/ActionList";
import AdPopup from "./components/PopAd";
import Topics from "./components/Topics";
import PrivacyPopup from "../../components/privacy-popup";
// import MenuView from "./components/menuView";
// import SubBanner from "./components/SubBanner";
// import MainBanner from "./components/banner";
import "./index.scss";

// import { getActionList } from '../api/modules/home';

const BizPage = {
  CAR_WASH: "carwash",
};
interface Props {}
interface MainMenu {
  id: number;
  sharecontent: any;
  shareurl: any;
  superscriptFlag: number;
  superscriptImage: string;
  thumb: string;
  title: string;
  typeflag: number;
  url: any;
}
interface AdLoop {
  articleTitle: string;
  endTime: any;
  id: number;
  image: string;
  permCode: number;
  sid: string;
  sort: number;
  startTime: any;
  title: string;
  type: number;
  url: string;
}
interface Menu {
  id: number;
  sharecontent: any;
  shareurl: any;
  superscriptFlag: number;
  superscriptImage: string;
  thumb: string;
  title: string;
  typeflag: number;
  url: string;
}
interface News {
  articleTitle: string;
  endTime: any;
  id: number;
  image: string;
  permCode: number;
  sid: string;
  sort: number;
  startTime: any;
  title: string;
  type: number;
  url: string;
}
interface PicLoop {
  articleTitle: string;
  endTime: any;
  id: number;
  image: string;
  permCode: number;
  sid: string;
  sort: number;
  startTime: any;
  title: string;
  type: number;
  url: string;
}

interface AdMenu {
  id: number;
  sharecontent: any;
  shareurl: any;
  superscriptFlag: number;
  superscriptImage: string;
  thumb: string;
  title: string;
  typeflag: number;
  url: string;
}
interface Banner {
  articleTitle: string;
  endTime: any;
  id: number;
  image: string;
  permCode: number;
  sid: string;
  sort: number;
  startTime: any;
  title: string;
  type: number;
  url: string;
}

interface Room {
  roomId: number;
  feedsImg: string;
  liveStatus: number;
}
interface State {
  pageBg: string; // 页面背景
  menuBg: string; // 菜单背景
  couldOpenApp: boolean; // 菜单背景
  isScroll: boolean; // 是否滚动页面
  actions: [];
  mainMenu: MainMenu[];
  adLoop: AdLoop[];
  menulist: Menu[];
  newsList: News[];
  picLoop: PicLoop[];
  adMenu: AdMenu[];
  banner: Banner[]; // banner图片
  couldCheckIn: boolean;
  liveRooms: Room[];
  currentIndex: Number;
  bannerCurrent: Number;
  popupInfo: any;
  showAdPop: boolean;
  privacyAgreeStatus: boolean;
  showPrivacyPop: boolean;
  isWeapp: boolean;
  topics: any[];
}

export default class Index extends Component<Props, State> {
  private mainBannerView;

  constructor(props) {
    super(props);
    const isWeapp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP;

    this.state = {
      isScroll: false,
      pageBg: "",
      menuBg: "",
      couldOpenApp: getOpenAppFlag(),
      actions: [],
      mainMenu: [],
      adLoop: [],
      menulist: [],
      newsList: [],
      picLoop: [],
      adMenu: [],
      banner: [],
      topics: [],
      couldCheckIn: true,
      liveRooms: [],
      currentIndex: 0,
      bannerCurrent: 0,
      popupInfo: null,
      showAdPop: true,
      privacyAgreeStatus: false, // 是否同意隐私政策
      showPrivacyPop: true, // 是否显示隐私政策弹窗
      isWeapp: isWeapp,
    };
  }

  componentDidMount() {
    this.init();
    // copy(123)
    this.getLiveRooms();
  }
  launchAppError(err) {
    showModal("提示", err && err.errMsg);
  }

  componentWillUnmount() {}

  componentDidShow() {
    // this.$scope.getTabBar().$component.setState({
    //   currentIndex: 0
    // });
    // Taro.setNavigationBarColor({
    //   frontColor: '#ffffff',
    //   backgroundColor: '#ff0000',
    // animation: {
    //   duration: 400,
    //   timingFunc: 'easeIn'
    // }
    // })
    this.setState({
      couldOpenApp: getOpenAppFlag(),
    });
  }
  getLiveRooms() {
    getRoomList({
      pageNo: 1,
      pageSize: 100,
    })
      .then((res) => {
        let rooms = [];
        if (res?.list.length > 0) {
          rooms = this.filterLiveRoom(res.list);
        }
        this.setState({
          liveRooms: rooms,
        });
      })
      .catch((err) => {
        // showToast(err);
        console.error(err);
      });
  }
  componentDidHide() {}

  onPullDownRefresh() {
    this.init()
      .then((res) => {
        Taro.stopPullDownRefresh();
      })
      .catch((err) => {
        Taro.stopPullDownRefresh();
      });

    this.getLiveRooms();
  }

  onPageScroll(e) {
    // console.log(ev, 'dd');<T>(arg: T): T
  }
  touchStart(e) {
    this.setState({
      isScroll: true,
    });
  }
  touchEnd(e) {
    this.setState({
      isScroll: false,
    });
  }
  filterLiveRoom(rooms) {
    let newrooms = [];
    // 直播间状态。101：直播中，102：未开始，103已结束，104禁播，105：暂停，106：异常，107：已过期
    newrooms = rooms.filter(
      (item) => item.liveStatus === 101 || item.liveStatus === 102
    );
    return newrooms;
  }
  // 打卡新页面
  toPage(url: string, item?: any) {
    // 跳转小程序页面
    if (item && item.typeflag == 11) {
      let _url = /^\/.*/.test(url) ? url : `/${url}`; // 路径斜杠开头，兼容管理端配置时出错
      Taro.navigateTo({
        url: _url,
      });
      return;
    }
    pushWebView(url);
  }
  toLiveRoom() {
    const loggedIn = getLoggedIn();
    if (!loggedIn) {
      login();
    } else {
      Taro.navigateTo({
        url: "/pages/wxlive/index",
      });
    }
  }
  toChatRoom() {
    Taro.navigateTo({
      url: "/pages/live/live",
    });
  }
  checkIn() {
    const loggedIn = getLoggedIn();
    if (!loggedIn) {
      login();
    } else {
      // Taro.switchTab({
      //   url: "/pages/user/index"
      // });
      pushWebView(Pages.USER_SIGN, {
        share: false,
        // requireSignIn: true,
      });
    }
  }
  closePop() {
    this.setState({
      showAdPop: false,
    });
    // Taro.showTabBar()
  }
  handleChange(e) {
    // console.log(e.detail.current);
    this.setState({
      currentIndex: e.detail.current,
    });
  }
  swiperChange(e) {
    this.setState({ bannerCurrent: e.detail.current });
  }
  onShareAppMessage() {
    return {
      title: "交广领航-服务爱车生活",
      path: "/pages/index/index",
    };
  }
  onShareTimeline() {
    return {
      title: "交广领航-服务爱车生活",
      query: "from=timeline",
    };
  }
  // 处理用户隐私协议事件回调
  handleAgreePrivacyAuthorize(status: boolean) {
    this.setState({
      privacyAgreeStatus: status,
      showPrivacyPop: false,
    });
    !status && Taro.exitMiniProgram();
  }
  init() {
    // wx.setTabBarBadge({
    //   index: 2,
    //   // text: ''
    // })
    // 核心数据加载
    return Promise.all([getHomeData(), getWeappHomeConfig(), getTopicData()])
      .then(([home, config, topics]) => {
        this.setState({
          pageBg: home.homepageBackground.bg1,
          menuBg: home.homepageBackground.bg3,
          // 有buttonList直接用配置中的buttonList，否则使用接口返回的，并进行白名单过滤
          mainMenu:
            config.buttonList && config.buttonList.length
              ? config.buttonList
              : home.menuButton.menulist
                  .filter((m) => {
                    const list = config.buttonWhiteList;
                    // 白名单为空表示不过滤
                    if (!list.length) return true;
                    return list.some((key) => {
                      return m.title.indexOf(key) > -1;
                    });
                  })
                  .slice(0, 10), // 最多显示10个按钮
          adLoop: home.commonAppLoopAction,
          menulist: home.menuButton.menulist,
          newsList: home.middleAppLoopAction,
          picLoop: home.bottomAppLoopAction,
          adMenu: home.menuButton.adMenuList,
          // 不展示banner
          banner: home.topAppLoopAction,
          topics: topics?.topicSettingItemReqDtoList || [],
        });
        if (home.topAppLoopAction.length) {
          setTimeout(() => {
            this.mainBannerView &&
              this.mainBannerView.setBannerList(home.topAppLoopAction);
          }, 500);
        }
        // this.mainBannerView.setBannerList(home.topAppLoopAction);

        // 异步加载非核心数据
        this.loadActionList(config);
        this.loadAdData();
      })
      .catch((err) => {
        showToast(err);
        console.error(err);
        return Promise.reject(err);
      });
  }

  // 加载活动列表数据
  loadActionList(config) {
    getActionList()
      .then((actions) => {
        this.setState({
          // 根据黑名单关键词配置过滤活动列表
          actions: actions.filter((item) => {
            const isBlack = config.actionBlackList.some((word) => {
              return item.title.indexOf(word) > -1;
            });
            return !isBlack;
          }),
        });
      })
      .catch((err) => {
        console.error("加载活动列表失败:", err);
        // 设置默认值，避免页面报错
        this.setState({
          actions: [],
        });
      });
  }

  // 加载广告数据
  loadAdData() {
    getHomeAdData()
      .then((popups) => {
        this.setState({
          popupInfo: popups && popups.frameAd && popups.frameAd[0],
        });
      })
      .catch((err) => {
        console.error("加载广告数据失败:", err);
        // 设置默认值，避免页面报错
        this.setState({
          popupInfo: null,
        });
      });
  }

  render() {
    let {
      pageBg,
      mainMenu,
      adLoop,
      // menuBg,
      actions,
      // menulist,
      // newsList,
      // picLoop,
      adMenu,
      banner,
      liveRooms,
      currentIndex,
      couldCheckIn,
      isScroll,
      bannerCurrent,
      popupInfo,
      showAdPop,
      showPrivacyPop,
      isWeapp,
      topics,
    } = this.state;

    return (
      <IndexLayout pageBg={pageBg}>
        <View
          className="pageBox"
          onTouchStart={this.touchStart.bind(this)}
          onTouchEnd={this.touchEnd.bind(this)}
        >
          <Search
            className="jglh-search"
            disabled
            shape="round"
            background="none"
            placeholder="请输入搜索关键词"
            onClick={this.toPage.bind(this, Pages.JGLH_SEARCH)}
          />
          {banner.length > 0 && (
            <View className="rotation">
              <Swiper
                className="bg-box"
                circular
                autoplay
                onChange={this.swiperChange.bind(this)}
              >
                {banner.map((item) => {
                  return (
                    <SwiperItem key={item.id} className="swiper-item">
                      <View
                        className="bg-item"
                        onClick={this.toPage.bind(this, item.url)}
                      >
                        <Image
                          mode="aspectFit"
                          src={getFullImagePath(
                            item.image,
                            "?imageView2/0/format/jpg/imageslim"
                          )}
                          className="bgImage"
                        />
                      </View>
                    </SwiperItem>
                  );
                })}
              </Swiper>

              {/* 自定义指示点 */}
              {banner.length > 1 && (
                <View className="spot-pagination">
                  {banner.map((item: any, index: number) => (
                    <View
                      key={index}
                      className={
                        "spot-pagination-bullet " +
                        (bannerCurrent == index
                          ? "spot-pagination-bullet-active"
                          : "")
                      }
                    />
                  ))}
                </View>
              )}
            </View>
          )}
          {/* 按钮区 */}
          <View className="new-menu">
            {/* <View
              className="menu-item"
              onClick={this.toChatRoom.bind(this)}
            >
              <View className="menu-item-img">
                <Image src={getFullImagePath('FiWa6fNz0g-4HkQ9OQuwrUz4DTBo')} className="img" />
              </View>
              <Text className="menu-item-text">聊天室</Text>
            </View> */}
            {mainMenu.map((item) => {
              return (
                <View
                  className="menu-item"
                  hoverClass="menu-item-hover"
                  key={item.id}
                  onClick={this.toPage.bind(this, item.url, item)}
                >
                  <View className="menu-item-img">
                    <Image src={getFullImagePath(item.thumb)} className="img" />
                    {item.superscriptFlag === 1 && (
                      <Image
                        src={getFullImagePath(item.superscriptImage)}
                        className="brdige"
                      />
                    )}
                  </View>
                  <Text className="menu-item-text">{item.title}</Text>
                </View>
              );
            })}
          </View>

          {/* 广告条 */}
          {adLoop.length > 0 && (
            <View className="ad-loop">
              {adLoop.length === 1 &&
                adLoop.map((item) => {
                  return (
                    <Image
                      key="index"
                      className="img"
                      src={getFullImagePath(item.image)}
                      onClick={this.toPage.bind(this, item.url)}
                    />
                  );
                })}
              {adLoop.length > 1 && (
                <Swiper className="ad-swuper" circular>
                  {adLoop.map((item) => {
                    return (
                      <SwiperItem
                        key={item.id}
                        onClick={this.toPage.bind(this, item.url)}
                      >
                        <Image
                          className="img"
                          src={getFullImagePath(item.image)}
                        />
                      </SwiperItem>
                    );
                  })}
                </Swiper>
              )}
            </View>
          )}

          {/*
        {newsList.length > 0 && (
          <View className='news'>
            <View className='news-img'>
              <Image
                className='img'
                src={getFullImagePath(newsList[0].image)}
              ></Image>
            </View>
            <View className='news-list'>
              <Swiper className='news-swuper' vertical autoplay interval={2000}>
                {newsList.map(item => {
                  return (
                    <SwiperItem
                      className='news-item'
                      onClick={this.toPage.bind(this, item.url)}
                      key={item.id}
                    >
                      <View className='news-text'>{item.title}</View>
                    </SwiperItem>
                  );
                })}
              </Swiper>
            </View>
          </View>
        )} */}

          {liveRooms.length > 0 && (
            <View className="live">
              <View className="container" onClick={this.toLiveRoom.bind(this)}>
                <View className="text">
                  <View className="online" />
                  <View className="detail">点击查看</View>
                </View>
                <View>
                  <Swiper
                    className="room-swiper"
                    circular
                    // autoplay
                    // interval={3000}
                    onChange={this.handleChange.bind(this)}
                    previous-margin="40rpx"
                    next-margin="40rpx"
                  >
                    {liveRooms.map((item, index) => {
                      return (
                        <SwiperItem
                          key={item.roomId}
                          className={`item ${
                            currentIndex == index ? "active" : ""
                          }`}
                        >
                          <Image className="img" src={item.feedsImg} />
                        </SwiperItem>
                      );
                    })}
                  </Swiper>
                </View>
              </View>
            </View>
          )}

          {topics.length > 0 && (
            <Topics toPage={this.toPage.bind(this)} list={topics} />
          )}
          {adMenu.length > 0 && (
            <View className="bus">
              {adMenu.slice(-4).map((item) => {
                return (
                  <View
                    className="block"
                    onClick={this.toPage.bind(this, item.url)}
                    key={item.id}
                  >
                    <Image className="img" src={getFullImagePath(item.thumb)} />
                  </View>
                );
              })}
            </View>
          )}
          {actions.length > 0 && (
            <ActionList toPage={this.toPage.bind(this)} list={actions} />
          )}
          {couldCheckIn && (
            <View
              className={`breathing-btn ${isScroll ? "breathingStart" : ""}`}
              onClick={this.checkIn.bind(this)}
            >
              <Text className="btn-checkin">签到</Text>
            </View>
          )}

          {popupInfo && showAdPop && (
            <AdPopup
              toPage={this.toPage.bind(this)}
              frameAd={popupInfo}
              close={this.closePop.bind(this)}
            />
          )}
          {/* <Navigator url='/pages/wxlive/index' className='btn-live btn-checkin'>直播间</Navigator> */}
          {/* <View onClick={this.toPage.bind(this, 'https://dev.jgrm.net/actions/app/mcar/#/camera/index')} className='btn-live btn-checkin'>
          违法曝光
        </View> */}
          {/* {this.state.couldOpenApp && (
            <View className="float-btn" hoverClass='float-btn-hover'>
              <Button
                className="btn-openapp"
                type="default"
                open-type="launchApp"
                app-parameter="jglh-weapp"
                onError={this.launchAppError.bind(this)}
              >
                <Text>打开</Text>
                <Text>App</Text>
              </Button>
            </View>
          )} */}
          {/* <View style="height: 66px;background: #f2f4f5;"></View> */}
        </View>
        {isWeapp && (
          <PrivacyPopup
            authorize={this.handleAgreePrivacyAuthorize.bind(this)}
            showPop={showPrivacyPop}
          />
        )}
      </IndexLayout>
    );
  }
}
