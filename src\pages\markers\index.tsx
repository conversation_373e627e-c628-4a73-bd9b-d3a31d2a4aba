import Taro from "@tarojs/taro";
import React, { Component } from "react";
import { View, Image, Text } from "@tarojs/components";
import { Icon, Popup } from "@antmjs/vantui";
import { pushWebView, reverseGeocoder, getAudioURL } from "@/tools";
import { getImageURL } from "@/util";
import { formatRelativeTime } from "@/util/time";

import Audio from "@/pages/live/components/LiveMessage/MessageItem/components/Audio";
import styles from "@/pages/live/components/LiveMessage/MessageItem/index.module.less";
import "./index.scss";

interface Props {}

interface State {
  markerIcons: string[];
  trafficTypes?: any[];
  markers: any[];
}

export default class Index extends Component<Props, State> {
  eventChannel: any = null; // 事件通道
  constructor(props) {
    super(props);

    this.state = {
      markerIcons: [
        "Fmo8T6lD4zzAdUDFOU3cA7RegK1f",
        "Fv8OVPvy4jxbG7S_Rd5ebb5XJqZZ",
        "Finod3rc9sekJUJtuFT1NaXB0v-g",
        "FolgpiIBMuKMGrvoZP-0D_nTsqag",
        "FjPy1BpE2pF3Il7M7VskV6KSqGfv",
        "FtP5GKB5XjQgC5cAYFXPLeUag1sJ",
        "FoGdMInsv0AflB6e9qc3_sB432P6",
        "FpSGTl7ZHlUfYqDeuQ-QQvm8eGrL",
        "Fs4jJM4nOE8kqEc_lO0yZR1CovA6",
      ],
      trafficTypes: [
        {
          icon: "FifxNhGNpNEOEfhwgUlQtgIz8vIv",
          text: "道路通畅",
          value: 1,
        },
        {
          icon: "FmaBOW1tRkfXYj6kBlFBpEmJWwZ1",
          text: "道路拥堵",
          value: 2,
        },
        {
          icon: "FnjOyi_k2w61j42nQr4c7YmJOC4l",
          text: "交通事故",
          value: 3,
        },
        {
          icon: "Fna3jROHUgrJffoxYve0FJiHEvVl",
          text: "设施故障",
          value: 6,
        },
        {
          icon: "Fiq95pIB2UxdfXCOJhGkvH4rqngq",
          text: "道路施工",
          value: 4,
        },
        {
          icon: "Fj8qoTX_7iqy9WiFrQISsW_pN-5S",
          text: "交通管制",
          value: 7,
        },
      ],
      markers: [], // 所有标记的位置(经纬度、详细地址)
    };
  }

  componentDidMount() {
    let that = this;
    // 接受上个页面传递的参数
    const pages = Taro.getCurrentPages();
    const current = pages[pages.length - 1];
    const eventChannel = current.getOpenerEventChannel();

    // 接收 map页面的 events 中的 transData 传递的数据
    eventChannel.on("transData", (res) => {
      res = JSON.parse(JSON.stringify(res || []));
      that.setState({
        markers: res.reverse(),
      });
    });
    this.eventChannel = eventChannel;
  }

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}
  toPage(url: string) {}

  handleImageClick(marker) {
    if (marker.type == 5) {
      Taro.previewImage({
        urls: [getImageURL(marker.image)] // 需要预览的图片http链接列表
      })
    }
  }

  handleMarkerClick(marker) {
    this.eventChannel.emit("selectMarker", marker);
    Taro.navigateBack();
  }

  getTypeTitle(type) {
    const { trafficTypes } = this.state;
    let matchTitle = (trafficTypes || []).find((item) => item.value === type);
    return matchTitle?.text || "";
  }
  formatTitle(msg) {
    let str = "";
    switch (msg.type) {
      case 8:
        str = "语音路况";
        break;
      case 5:
        str = "图片路况";
        break;
      case 1:
      case 2:
      case 3:
      case 4:
      case 6:
      case 7:
        str = this.getTypeTitle(msg.type);
        break;
    }
    return str;
  }
  getTypeIcon(type) {
    const { trafficTypes } = this.state;
    let matchTitle = (trafficTypes || []).find((item) => item.value === type);
    return matchTitle?.icon || "";
  }
  formatImage(msg) {
    let key = "";
    switch (msg.type) {
      case 8:
        key = "FmRJoGUQeuIBTE9AP2W3DPsT-urt"; // 语音图标
        break;
      case 5:
        key = msg.image;
        break;
      case 1:
      case 2:
      case 3:
      case 4:
      case 6:
      case 7:
        key = this.getTypeIcon(msg.type);
        break;
    }
    return getImageURL(key);
  }

  render() {
    let { markers } = this.state;
    // let { playStatus } = this.state;

    return (
      <View className="markers-page">
        {!!markers.length &&
          markers?.map((item, index) => {
            return (
              <View key={index} className="marker-item" onClick={this.handleMarkerClick.bind(this, item)}>
                {/* 图片地址 */}
                <Image
                  src={this.formatImage(item)}
                  className="left-image"
                  mode="aspectFit"
                  onClick={this.handleImageClick.bind(this, item)}
                />
                <View className="right-content">
                  <View className="content-top">
                    <View className="title">{this.formatTitle(item)}</View>
                    <View className="desc">
                      来自
                      <Text className="name">
                        {item?.sender?.name ? item.sender.name : "车友"}
                      </Text>
                    </View>
                    <View className="time">{formatRelativeTime(item?.t)}</View>
                  </View>
                  <View className="content-bottom">
                    {item.type == 8 && (
                      <View className="popup-voice-msg">
                        <View
                          className={`voice-msg-item ${styles.messageItem} ${styles.right}`}
                        >
                          <View className={styles.body}>
                            <View
                              className={`${styles.content} ${styles.text}`}
                            >
                              {/* <Text content={`语音${props.message.speech}`} /> */}
                              <Audio
                                className="voice-msg-item-audio"
                                speech={item?.speech}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    )}
                    {item.type != 8 && (
                      <View className="address-detail">{item?.road?.name}</View>
                    )}
                    <View className="directions">{item?.road?.direction}</View>
                    {!!item?.text && (
                      <View className="remark">{item?.text}</View>
                    )}
                  </View>
                </View>
              </View>
            );
          })}
      </View>
    );
  }
}
