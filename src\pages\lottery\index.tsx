import Taro, { getCurrentInstance, useDidShow } from "@tarojs/taro";
import React, { useState, useEffect, useMemo, useCallback } from "react";
import { View, Text, Image, RichText } from "@tarojs/components";
import { But<PERSON>, Loading } from "@antmjs/vantui";
import dayjs from "dayjs";
import { showToast } from "@/tools";
import { getLoggedIn } from "@/gdata";
import { getImageURL } from "@/util";
import Banner from "./components/Banner";
import { getLotteryDetail, subscribeLottery } from "@/api/modules/lottery";

import "./index.scss";
// 1.日期时间处理
// 2.接口请求
// 3.规则富文本

// 添加接口类型定义
interface Prize {
  id: number;
  prizeName: string;
  prizeImages: string;
}

interface LotteryDetail {
  id: number;
  sdate: number;
  edate: number;
  openTime: number;
  applied: boolean;
  prizes: Prize[];
  activityInfo: string;
  shareTitle: string;
  shareImage: string;
  shareContent: string;
}

const PrizePage = () => {
  const [loginStatus, setLoginStatus] = useState(true);
  const [lotteryDetail, setLotteryDetail] = useState<LotteryDetail>(
    {} as LotteryDetail
  );
  const [loading, setLoading] = useState(true);
  // 从路由参数中获取活动id
  const id = Taro.getCurrentInstance().router?.params?.id;
  // 将数据初始化
  const initData = useCallback(async () => {
    if (!id) {
      showToast("活动ID不存在");
      return;
    }

    try {
      setLoading(true);
      const res = await getLotteryDetail(id);
      setLotteryDetail(res);
      setLoginStatus(getLoggedIn());
      // 根据res.activityName设置navigationBarTitleText
      Taro.setNavigationBarTitle({
        title: res.activityName,
      });
    } catch (err) {
      showToast("获取活动详情失败");
    } finally {
      setLoading(false);
    }
  }, [id]);

  useDidShow(() => {
    initData();
  });
  // 处理参与抽奖
  const handleLottery = useCallback(() => {
    subscribeLottery(lotteryDetail.id)
      .then((res) => {
        if (res.templateId) {
          Taro.requestSubscribeMessage({
            tmplIds: [res.templateId],
            entityIds: [],
            success: function (result) {
              // 用户同意订阅消息
              if (result[res.templateId] === "accept") {
                Taro.showToast({
                  title: "参与成功，已订阅开奖通知",
                  icon: "success",
                  duration: 2000,
                });
              } else {
                // 用户拒绝订阅消息
                Taro.showToast({
                  title: "参与成功，未订阅开奖通知",
                  icon: "success",
                  duration: 2000,
                });
              }
            },
            fail: function () {
              // 订阅消息失败，但抽奖参与成功
              Taro.showToast({
                title: "参与成功",
                icon: "success",
                duration: 2000,
              });
            },
            complete: function () {
              initData();
            },
          });
        } else {
          // 没有模板ID的情况
          Taro.showToast({
            title: "参与成功",
            icon: "success",
            duration: 2000,
          });
          initData();
        }
      })
      .catch((err) => {
        Taro.showToast({
          title: "参与失败，请重试",
          icon: "error",
          duration: 2000,
        });
      });
  }, [lotteryDetail.id, initData]);
  // 处理登录
  const handleLogin = () => {
    Taro.navigateTo({ url: "/pages/login/index" });
  };

  const timeToDate = (time) => {
    return dayjs(time).format("M月D日 HH:mm");
  };

  // 添加判断按钮状态的方法
  const getBtnConfig = () => {
    if (!loginStatus) {
      return {
        text: "登录后参与抽奖",
        type: "danger",
        handler: handleLogin,
        disabled: false,
      };
    }

    const now = Date.now();
    const startTime = lotteryDetail?.sdate || 0;
    const endTime = lotteryDetail?.edate || 0;
    const openTime = lotteryDetail?.openTime || 0;

    if (now < startTime) {
      return {
        text: "活动未开始",
        type: "info",
        handler: () => {},
        disabled: true,
      };
    }

    if (now > openTime && lotteryDetail?.applied) {
      return {
        text: "已开奖,查看是否中奖",
        type: "primary",
        handler: () => {
          Taro.navigateTo({
            url: `/pages/lottery-result/index?id=${lotteryDetail.id}`,
          });
        },
        disabled: false,
      };
    }

    if (now > openTime && !lotteryDetail?.applied) {
      return {
        text: "活动已结束",
        type: "info",
        handler: () => {},
        disabled: true,
      };
    }

    if (lotteryDetail?.applied) {
      return {
        text: "已参与,等待开奖",
        type: "info",
        handler: () => {},
        disabled: false,
      };
    }

    return {
      text: "参与抽奖",
      type: "primary",
      handler: handleLottery,
      disabled: false,
    };
  };

  // 使用useMemo缓存计算结果
  const picList = useMemo(
    () =>
      lotteryDetail.prizes?.map((item, index) => ({
        id: index + 1,
        image: getImageURL(item.prizeImages),
        url: "",
      })) || [],
    [lotteryDetail.prizes]
  );

  const prizeName = useMemo(
    () => lotteryDetail.prizes?.map((item) => item.prizeName).join(",") || "",
    [lotteryDetail.prizes]
  );

  // 分享配置
  Taro.useShareAppMessage(() => {
    return {
      title: lotteryDetail.shareTitle
        ? lotteryDetail.shareTitle
        : `邀你免费抽奖 - ${prizeName}`,
      path: `/pages/lottery/index?id=${id}`,
      imageUrl: lotteryDetail.shareImage
        ? getImageURL(lotteryDetail.shareImage)
        : picList[0]?.image,
    };
  });

  return (
    <View>
      {loading ? (
        <View className="loading-container">
          <Loading type="spinner" size="24px">
            加载中...
          </Loading>
        </View>
      ) : (
        <View className="prize-page">
          <View className="prize-header">
            {/* 奖品图片 */}
            <Banner picLoop={picList} />

            {/* 奖品名称 */}
            <View className="prize-info">
              <Text className="prize-name">奖品: {prizeName}</Text>
              <Text className="prize-description">
                {timeToDate(lotteryDetail?.openTime)} 自动开奖
              </Text>
            </View>
          </View>

          {/* 抽奖规则 */}
          <View className="prize-rules">
            <Text className="rules-title">抽奖说明</Text>
            <RichText
              className="rules-description"
              nodes={lotteryDetail?.activityInfo}
            />
          </View>

          {/* 抽奖按钮 */}
          <View className="btn-wrap">
            <Button
              block
              type={getBtnConfig().type as "default" | "danger" | "info"}
              className="prize-btn"
              onClick={getBtnConfig().handler}
              disabled={getBtnConfig().disabled}
            >
              {getBtnConfig().text}
            </Button>
          </View>
        </View>
      )}
    </View>
  );
};

export default PrizePage;
