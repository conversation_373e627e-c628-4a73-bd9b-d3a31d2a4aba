import Taro, { VideoContext, BackgroundAudioManager } from '@tarojs/taro'
import React, { Component }  from 'react'
import { View, Image, Video } from '@tarojs/components'

import './index.scss'


interface Props {
  videoUrl: string,
  parentUpdate: Function
}

interface Audio {
  src: string,
  title: string,
  coverImgUrl:string,
}

interface State {
  isLoad: number,
  isAudio: boolean,
  isFirst: boolean,
}

export default class Index extends Component<Props, State> {

  private videoContext: VideoContext
  private audio:BackgroundAudioManager
  private errArr:any = {
    '10001': '系统错误',
    '10002': '网络错误',
    '10003': '文件错误',
    '10004': '格式错误',
    '-1': '位置错误'
  };

  // config: PageConfig = {
  //   navigationBarTitleText: '播放区域',
  // }

  constructor(props) {
    super(props);

    console.log('ddddd');
    this.state = {
      isLoad: 0, // 0 : 初始化， 1： 加载中，2：开始播放。 3：音频加载中，4：音频播放
      isAudio: false,
      isFirst: true
    };
  }

  componentDidMount () {
    this.audio = Taro.getBackgroundAudioManager();
  }

  componentWillUnmount () { }

  componentDidShow () {
  }

  componentDidHide () { }
  toPage() {

  }

  componentDidUpdate() {
    this.videoContext = Taro.createVideoContext('myVideo');
  }

  showTip(str:string) {
    Taro.showToast({
      title: str,
      icon: 'none'
    });
  }

  playAudio(obj: Audio) {
    this.setState({
      isAudio: true,
      isLoad: 3
    });
    this.audio.src = obj.src;
    this.audio.title = obj.title;
    this.audio.coverImgUrl = obj.coverImgUrl;
    this.audio.protocol = 'hls';

    this.audio.onWaiting(() => {
      console.log('ddddd');
    });
    this.audio.onError((err?:any) => {
      console.log(err, '44444444');
      this.setState({
        isLoad: 3
      });
      this.showTip(this.errArr[err.errCode]);
    });
    this.audio.onPlay((err?:any) => {
      console.log(err, '12121212');
      this.setState({
        isLoad: 4
      });
    });
    if (this.audio) {
      this.audio.seek(0);
    }
    if (this.videoContext) {
      this.videoContext.stop();
    }
    this.audio.play();
  }

  // 播放直播视频
  playVideo() {
    this.setState({
      isLoad: 1,
      isFirst: false
    }, () => {
      this.videoContext.play();
    });
  }

  binVideoPlay(ev:any) {
    console.log(ev, '44');
    this.setState({
      isLoad: 2,
    });
  }

  binBideoErr(ev:any) {
    console.log(ev, '6666');
  }

  playTv() {
    if (this.audio) {
      this.audio.stop();
    }

    this.setState({
      isLoad: 1,
      isAudio: false
    }, () => {
      this.videoContext.play();
      this.props.parentUpdate();
    });
  }

  render () {
    let { videoUrl } = this.props;
    let { isLoad, isAudio } = this.state;
    let myVideoStyle = isLoad == 2 ? 'opacity: 1;z-index: 5' : '';
    let coverImage;
    if (isAudio) {
      if (isLoad == 4) {
        coverImage = '../../images/vadio_default_play.png';
      } else {
        coverImage = '../../images/vadio_default.jpg';
      }

    } else {
      coverImage = '../../images/vedio_default.png';
    }


    return (
      <View className="play-wrap">
          <Image className="play-img" src={coverImage} />
          { isLoad >= 3 && <View className="tv" onClick={this.playTv.bind(this)} />}
          {
            (isLoad > 2 && isLoad != 4) && <View className="player-btn" onClick={this.playVideo.bind(this)} />
          }
          {
            (isLoad == 1 || isLoad == 3) && <Image src="../../images/z.gif" className="rotate" />
          }
          {
            isLoad == 4 && <Image src="../../images/vadio_play_gif.gif" className="wave" />
          }

          {
            isLoad < 3 &&
            <Video
              onClick={this.playVideo.bind(this)}
              src={videoUrl}
              id="myVideo"
              className="myVideo"
              style={myVideoStyle}
              initialTime={0}
              onPlay={this.binVideoPlay.bind(this)}
              onError={this.binBideoErr.bind(this)}
              poster="https://dev.jgrm.net/resource/image/8168685bc438410b9c7b9d208ac2b010"
            />
          }

        </View>
    )
  }
}
