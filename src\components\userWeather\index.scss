.city-wrap {
  position: absolute;
  left: 16px;
  top: 50%;
  color: #000;
  transform: translateY(-50%);
}
.ava {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  margin-right: 10px;
  // color: #000;
  vertical-align: middle;
}

.weat {
  display: inline-block;
  // color: #fff;
  line-height: 1;
  vertical-align: middle;
  text-align: left;
  .city {
    display: block;
    text-align: justify;
    font-size: 14PX; 
    font-weight: 500;
  }

  .w-c {
    display: block;
    margin-top: 4px;
    font-size: 10PX;
    color: rgb(73, 73, 73);
  }
}