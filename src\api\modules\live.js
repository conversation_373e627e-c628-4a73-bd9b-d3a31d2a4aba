import API from '../api';
import { doGet } from '../request'

// 获取直播信息
API.extend({
  '/live/list/data': '/Radio/init', // 获取直播信息
  '/live/playback/data': '/Radio/programs/any_radio/review?day=1', // 获取回放信息
  // 微信小程序直播
  '/live/room/list': '/Radio/app/api/wx/live/room/infos', // 获取回放信息
});

export function getLiveInfo() {
  return doGet(API.dget('/live/list/data'));
}

export function getPlaybackInfo() {
  return doGet(API.dget('/live/playback/data'));
}

export function getRoomList(params) {
  return doGet(API.dget('/live/room/list'), params);
}
export const ret = {};
