import React from "react";
import Taro from "@tarojs/taro";
import { View, Image, Swiper, SwiperItem } from "@tarojs/components";
import { getImageURL, pushWebView, getFullImagePath } from "@/tools";
import "./index.scss"; // 引入样式文件

interface Card {
  articleTitle: string | null;
  hideFlag: number;
  id: number;
  image: string;
  permCode: number;
  position: number;
  sid: string;
  url?: string;
}
const Topics: React.FC<{
  list: any[];
  toPage: (value: string) => void;
}> = (props) => {
  const [cards, setCards] = React.useState<Card[]>([]);
  const [bulletCurrent, setBulletCurrent] = React.useState(0);
  const [swiperAutoplay, setAutoplay] = React.useState(false);

  React.useEffect(() => {
    // props.list根据position的值进行排序
    let position1 = props.list.filter((item) => item.position === 1);
    if (position1.length > 0) {
      setCards(position1);
    }
    setTimeout(() => {
      setAutoplay(true);
    }, 1500);
  }, [props.list]);

  const toPage = (item) => {
    // 跳转小程序页面
    if (item.permCode == 11) {
      let _url = /^\/.*/.test(item.url) ? item.url : `/${item.url}`; // 路径斜杠开头，兼容管理端配置时出错
      Taro.navigateTo({
        url: _url,
      });
      return;
    }
    if (item.permCode == 5) {
      Taro.navigateTo({
        url: "/pages/map/index",
      });
      return;
    }
    pushWebView(item.url);
  };
  const swiperChange = (e) => {
    setBulletCurrent(e.detail.current);
  };
  return (
    <View className="topics-container">
      <View className="top">
        <View className="left">
          <Swiper
            className="card-swiper"
            circular
            autoplay={swiperAutoplay}
            onChange={swiperChange}
          >
            {cards.map((item) => {
              return (
                <SwiperItem
                  key={item.id}
                  onClick={() => toPage(item)}
                  className="swiper-item"
                >
                  <View className="swiper-item_view">
                    <Image className="img" src={getFullImagePath(item.image)} />
                  </View>
                </SwiperItem>
              );
            })}
          </Swiper>
          {/* 自定义指示点 */}
          {cards.length > 1 && (
            <View className="spot-pagination">
              {cards.map((item: any, index: number) => (
                <View
                  key={index}
                  className={
                    "spot-pagination-bullet " +
                    (bulletCurrent == index
                      ? "spot-pagination-bullet-active"
                      : "")
                  }
                />
              ))}
            </View>
          )}
        </View>
        <View className="right">
          <View className="right-top">
            {props.list
              .filter((item) => item.position === 2)
              .map((item) => {
                return (
                  <Image
                    key={item.id}
                    className="img"
                    onClick={() => toPage(item)}
                    src={getFullImagePath(item.image)}
                  />
                );
              })}
          </View>
          <View className="right-bottom">
            {props.list
              .filter((item) => item.position === 3)
              .map((item) => {
                return (
                  <Image
                    key={item.id}
                    className="img"
                    onClick={() => toPage(item)}
                    src={getFullImagePath(item.image)}
                  />
                );
              })}
          </View>
        </View>
      </View>
      <View className="bottom">
        <View className="bottom1">
          {props.list
            .filter((item) => item.position === 4)
            .map((item) => {
              return (
                <Image
                  key={item.id}
                  className="img"
                  onClick={() => toPage(item)}
                  src={getFullImagePath(item.image)}
                />
              );
            })}
        </View>
        <View className="bottom2">
          {props.list
            .filter((item) => item.position === 5)
            .map((item) => {
              return (
                <Image
                  key={item.id}
                  className="img"
                  onClick={() => toPage(item)}
                  src={getFullImagePath(item.image)}
                />
              );
            })}
        </View>
        <View className="bottom3">
          {props.list
            .filter((item) => item.position === 6)
            .map((item) => {
              return (
                <Image
                  key={item.id}
                  className="img"
                  onClick={() => toPage(item)}
                  src={getFullImagePath(item.image)}
                />
              );
            })}
        </View>
      </View>
    </View>
  );
};

export default Topics;
