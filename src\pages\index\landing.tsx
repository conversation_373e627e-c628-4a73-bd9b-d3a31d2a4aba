import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { View } from '@tarojs/components'
import { getPageURL, pushWebView } from '../../tools'
import { getURLSearchParams } from '@/tools'

interface Props { }

interface State {}

enum LandingType {
  WebView = 'web',
  ThirdPart = '3rd'
}

export default class Index extends Component<Props, State> {

  // config: Config = {
  //   navigationBarTitleText: ''
  // }

  constructor(props: Props) {
    super(props);
    this.state = {};
  }

  componentDidMount () {
    this.initPage();
  }

  componentWillUnmount () { }

  componentDidHide () { }

  onError(e) {
    console.error(e);
  }

  onLoad(e) {
    console.log(e);
  }

  initPage() {
    const options = Taro.getLaunchOptionsSync();
    const q = decodeURIComponent(options.query.q);
    const params = getURLSearchParams(q);
    if (params.type === LandingType.WebView) {
      const url = String(params.url);
      pushWebView(url, { replace: true });
    } else {
      Taro.redirectTo({
        url: '/pages/index/index',
      })
    }
  }

  render () {
    return (
      <View className="loading">...</View>
    )
  }
}
