import React, { Component } from "react";
import Taro, { getCurrentInstance } from "@tarojs/taro";
import {
  Map,
  CoverView,
  Slot,
  View,
  Text,
  Image,
  Block,
} from "@tarojs/components";
import Audio from "@/pages/live/components/LiveMessage/MessageItem/components/Audio";
import SendBtn from "./components/SendBtn";
// import { Dialog as VDialog, Toast, Checkbox, Button } from '@antmjs/vantui'
import { pushWebView, getAudioURL } from "@/tools";
import { getUserInfo } from "@/gdata";
import { getImageURL } from "@/util";
import { initQiniuUploadToken } from "@/api/modules/chat";
import { getTrafficMsgs, reportTraffic } from "@/api/modules/traffic";
import PrizePopup from "@/components/prize-popup";

import styles from "@/pages/live/components/LiveMessage/MessageItem/index.module.less";
import "./index.scss";

interface Props {}

type QiNiuResponse = {
  key: string;
  hash: string;
};
interface State {
  showMap: boolean;
  playStatus: boolean;
  isIphone: boolean;
  currentAudioUrl: string;
  message?: any;
  userAvatar: string;
  currentMarker?: any;
  imgUploadToken?: string;
  showPrizePopup: boolean;
  prize?: any;
}

export default class Index extends Component<Props, State> {
  $instance: any = getCurrentInstance();

  // 实例变量，不参与页面更新
  currentAudio = ""; // 当前音频url
  audioContext: any = null; // audio 上下文
  constructor(props) {
    super(props);
    this.state = {
      showMap: false,
      playStatus: false,
      isIphone: false,
      currentAudioUrl: "",
      message: null, // 语音消息
      userAvatar: "",
      currentMarker: null, // 当前标记的位置(经纬度、详细地址)
      imgUploadToken: "",
      showPrizePopup: false,
      prize: null,
    };
  }

  toPage(type) {
    const { currentMarker } = this.state;
    Taro.redirectTo({
      url: `/pages/traffic-text-image/index?type=${type}&marker=${JSON.stringify(
        currentMarker
      )}`,
      // success: function (res) {
      //   // 通过eventChannel向被打开页面传送数据
      //   res.eventChannel.emit("transLocationData", currentMarker);
      // },
    });
  }

  chooseMedia(type) {
    const that = this;
    const { imgUploadToken, currentMarker } = this.state;
    Taro.chooseMedia({
      count: 1,
      mediaType: ["image"],
      success: (res) => {
        res.tempFiles.forEach((file) => {
          Taro.showLoading({
            title: "请稍等...",
          });
          Taro.uploadFile({
            url: UPLOAD_QINIU_URL,
            filePath: file.tempFilePath,
            name: "file",
            formData: { token: imgUploadToken },
          })
            .then((r) => {
              Taro.hideLoading();
              const result: QiNiuResponse = JSON.parse(r.data);
              if (result.key) {
                Taro.redirectTo({
                  url: `/pages/traffic-text-image/index?type=image&image=${
                    result.key
                  }&token=${imgUploadToken}&marker=${JSON.stringify(
                    currentMarker
                  )}`,
                });
              } else {
                Taro.showToast({
                  title: "上传失败",
                  icon: "none",
                });
              }
            })
            .catch((e) => {
              Taro.hideLoading();
              Taro.showToast({
                title: "上传失败",
                icon: "none",
              });
            });
        });
      },
    });
  }
  componentDidMount() {
    let that = this;

    // 接受上个页面传递的参数
    const pages = Taro.getCurrentPages();
    const current = pages[pages.length - 1];
    const eventChannel = current.getOpenerEventChannel();

    // 接收 map页面的 events 中的 transData 传递的数据
    eventChannel.on("transData", (res) => {
      that.setState({
        currentMarker: res,
      });
    });

    // 判断机型
    const model = Taro.getSystemInfoSync().model;
    const isIphone = /iphone/i.test(model);

    const userAvatar = getUserInfo()?.portrait
      ? getImageURL(getUserInfo()?.portrait)
      : getImageURL("Fr9WEh0tOeRea8vZjSgXgr2_K0By");
    // 创建 InnerAudioContext
    const audioContext = Taro.createInnerAudioContext();

    // 保存 InnerAudioContext 到状态
    this.audioContext = audioContext;
    this.setState({
      isIphone,
      userAvatar,
    });
    // 获取上传图片的token
    initQiniuUploadToken().then((token) => {
      this.setState({
        imgUploadToken: token,
      });
    });
  }

  fetchMsgs(params) {
    return getTrafficMsgs(params).then((res) => {
      return res || [];
    });
  }

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  onTap(e) {}
  // 设置语音消息
  setVoiceMsg(message) {
    if (!message) {
      this.audioContext && this.audioContext.stop();
    }
    this.setState({
      message,
      currentAudioUrl: "",
      playStatus: false,
    });
  }

  formatParams() {
    let { currentMarker, message } = this.state;
    let params = {
      msgType: 2, // 0:文字，1:图片，2:语音
      type: 8, // 8:语音
      x: currentMarker.longitude,
      y: currentMarker.latitude,
      audio: message.url,
      speech: message.speech,
      ptype: "", // 语音、文字路况时传空，图片路况时传TRAFFIC
    };
    return params;
  }

  // 上报路况
  handleReportSubmit() {
    const params = this.formatParams();
    const that = this;
    reportTraffic(params).then((res) => {
      Taro.showToast({
        title: "上报成功",
        icon: "none",
      });
      if (res.prize?.url !== "" && res.prize?.thumb) {
        this.setState({
          showPrizePopup: true,
          prize: res.prize,
        });
      } else {
        Taro.navigateBack();
      }
    });
  }

  onPlayAudio(url) {
    if (url && url === this.currentAudio) {
      if (this.state.playStatus) {
        this.audioContext.pause();
        this.setState({
          currentAudioUrl: "",
          playStatus: false,
        });
      } else {
        this.audioContext.play();
        this.setState({
          currentAudioUrl: url,
          playStatus: true,
        });
      }
    } else {
      this.audioContext && this.audioContext.stop();
      const audioContext = this.audioContext;

      // 修改 InnerAudioContext 的 src 属性
      audioContext.src = getAudioURL(url);

      // 使用 setState 的回调函数确保在状态更新完成后进行操作
      this.audioContext = audioContext;
      // 当音频可以播放就将状态从loading变为可播放
      if (this.state.isIphone) {
        // IOS环境需要立即调用
        this.audioContext.play();
      } else {
        this.audioContext.onCanplay(() => {
          console.log("onCanplay");
          this.audioContext.play();
        });
      }
      // 当音频在缓冲时改变状态为加载中
      this.audioContext.onWaiting(() => {
        console.log("onWaiting");
      });
      // 开始播放后更改图标状态为播放中
      this.audioContext.onPlay(() => {
        console.log("onPlay");
        this.currentAudio = url;
        this.setState({
          currentAudioUrl: url,
          playStatus: true,
        });
      });
      // 暂停后更改图标状态为暂停
      this.audioContext.onPause(() => {
        console.log("onPause");
        this.setState({
          playStatus: false,
        });
      });
      // 播放结束后更改图标状态
      this.audioContext.onEnded(() => {
        // console.log("onEnded");
        this.currentAudio = "";
        this.setState({
          currentAudioUrl: "",
          playStatus: false,
        });
      });
      // 播放出错
      this.audioContext.onError((e) => {
        // currentAudio.current = "";
        // setCurrentAudioUrl('')
      });
    }
  }

  toPrizePage(url) {
    if (!url) {
      return;
    }
    const params = [`url=${encodeURIComponent(url)}`, `share=0`].join("&");
    const path = `/pages/web/index?${params}`;
    Taro.redirectTo({
      url: path,
    });
  }

  render() {
    // let { mapMarkers, defaultPosition, showMap, playStatus } = this.state;
    let { playStatus, message, userAvatar, showPrizePopup, prize } = this.state;
    return (
      <View className="traffic-page">
        <Image
          className="banner-img"
          mode="widthFix"
          src={getImageURL("FhY2pIYMSGU_tYG1QtRhs2EUXJed")}
        />
        <View className="traffic-page-content">
          <View className="block-title">或用以下方式分享</View>
          <View className="report-types">
            <Image
              className="report-type"
              mode="widthFix"
              src={getImageURL("FvRzZdyXwP3E_8l9Zo3sHdL92xWA")}
              onClick={this.toPage.bind(this, "text")}
            />
            <Image
              className="report-type"
              mode="widthFix"
              src={getImageURL("FmOiHl8zrNGYbjnGz0flPAZ0fhKc")}
              onClick={this.chooseMedia.bind(this)}
            />
          </View>
          {/* 语音消息 */}
          <Block>
            {!!message && (
              <View className="voice-msg">
                <View
                  className={`voice-msg-item ${styles.messageItem} ${styles.right}`}
                >
                  <Image src={userAvatar} className={styles.messageAvatar} />
                  <View className={styles.body}>
                    <View
                      className={`${styles.content} ${styles.text} ${
                        playStatus ? styles.playing : ""
                      }`}
                      onClick={this.onPlayAudio.bind(this, message.url)}
                    >
                      {/* <Text content={`语音${props.message.speech}`} /> */}
                      <Audio speech={message.speech} />
                    </View>
                  </View>
                </View>
              </View>
            )}
            <SendBtn
              type="voice"
              setVoiceMsg={this.setVoiceMsg.bind(this)}
              reportSubmit={this.handleReportSubmit.bind(this)}
            />
          </Block>
        </View>
        <PrizePopup
          show={showPrizePopup}
          prizeImage={prize?.thumb || ""}
          onClose={() => {
            this.setState({ showPrizePopup: false });
            Taro.navigateBack();
          }}
          onImageClick={() => this.toPrizePage(prize?.url)}
        />
      </View>
    );
  }
}
