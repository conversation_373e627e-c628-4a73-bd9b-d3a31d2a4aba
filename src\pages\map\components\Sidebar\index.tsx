import React, { Component } from "react";
import { View, Image } from "@tarojs/components";
import { getImageURL } from "@/util";
import "./index.scss";

interface SidebarItem {
  id: number;
  thumb: string;
  highLightThumb: string;
  keyword: string;
  searchtype: string;
  type: number;
  showtype: number;
  url?: string;
  sort: number;
  pagesize?: number;
  bounddis?: number;
  searchurl?: string;
  searchitemurl?: string;
  sid: string;
}

interface Props {
  items: SidebarItem[];
  activeItemId?: number;
  onItemClick: (item: SidebarItem) => void;
}

interface State {}

export default class Sidebar extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {};
  }

  handleItemClick = (item: SidebarItem) => {
    this.props.onItemClick(item);
  };

  render() {
    const { items, activeItemId } = this.props;

    // 如果没有侧边栏数据，不渲染组件
    if (!items || items.length === 0) {
      return null;
    }

    // 按sort字段排序
    // const sortedItems = [...items].sort((a, b) => a.sort - b.sort);
    const sortedItems = [...items];

    return (
      <View className="sidebar">
        {sortedItems.map((item) => {
          const isActive = activeItemId === item.id;
          const iconPath =
            isActive && item.highLightThumb
              ? getImageURL(item.highLightThumb)
              : getImageURL(item.thumb);

          return (
            <View
              key={item.id}
              className={`sidebar-item ${isActive ? "active" : ""}`}
              onClick={() => this.handleItemClick(item)}
            >
              <Image
                className="sidebar-icon"
                src={iconPath}
                mode="aspectFit"
                onError={() => {
                  console.warn("侧边栏图标加载失败:", iconPath);
                }}
              />
            </View>
          );
        })}
      </View>
    );
  }
}
