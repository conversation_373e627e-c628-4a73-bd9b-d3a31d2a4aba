import React, { Component }  from 'react'
import Taro, { getCurrentInstance } from '@tarojs/taro'
import { View, Text, Image } from '@tarojs/components'
import { Dialog as VDialog, Toast, Checkbox, Button } from '@antmjs/vantui'
import { pushWebView } from '@/tools'
import { getImageURL } from "@/util";
import "./index.scss";

interface Props {  }

interface State {
  appid: string,
  path: string,
  name: string,
  url: string,
  checkedList: any,
  isOpened: boolean,
  show: boolean,
  checked: boolean,
  data?: any,
}

export default class Index extends Component<Props, State> {
  $instance: any = getCurrentInstance()
  checkboxOption = [{
    value: 'agree',
    label: 'jglh'
  }]
  constructor(props) {
    super(props);
    this.state = {
      appid: '',
      path: '',
      url: '',
      name: '',
      checkedList: ['agree'],
      isOpened: false,
      show: true,
      checked: true,
      data: {},
    };
  }

  componentDidMount () {
    // 初始化分享参数
    let appid = this.$instance.router.params.appid;
    let path = decodeURIComponent(this.$instance.router.params.path);
    let name = this.$instance.router.params.name ? decodeURIComponent(this.$instance.router.params.name) : ''; // 小程序名称
    let url = ''
    if (appid == 'wxc31aa6bbea26d448') {
      url = 'https://radio.jgrm.net/actions/app/agreement/edj.html'
      name = 'e代驾'
      Taro.setNavigationBarTitle({
        title: '代驾服务'
      })
    } else if (appid == 'wx1e52e535dc7bc26a') {
      url = 'https://radio.jgrm.net/actions/app/agreement/9zhoudj.html'
      name = '九州代驾'
      Taro.setNavigationBarTitle({
        title: '代驾服务'
      })
    } else {
      this.setState({
        show: false,
      })
      Taro.setNavigationBarTitle({
        title: '跳转小程序'
      })
    }
    this.setState({
      appid,
      path,
      url,
      name
    })
    console.log('appid==', appid)
    console.log('path==', path)
  }
  handleChange (value) {
    this.setState({
      checked: value.detail
    })
  }
  beforeClose (action) {
    if(action == 'confirm'){
      if(!this.state.checked){
        this.setState({
          isOpened: true
        })
        Toast.show('请勾选同意用户协议')
        return false
      }else{
        this.toWeapp()
        return true
      }
    }else{
      Taro.navigateBack()
      return true
    }
  }
  toPage () {
    pushWebView(this.state.url);
  }
  toWeapp() {
    Taro.navigateToMiniProgram({
      appId: this.state.appid,
      path: this.state.path,
      // extraData: {
      //   foo: 'bar'
      // },
      // envVersion: 'develop',
      success(res) {
        // 打开成功
        Taro.navigateBack()
      },
      fail(err) {
        // 打开失败
        Taro.navigateBack()
      },
    })
  }

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  render () {
    let { name, show, checked } = this.state;
    return (
      <View className="wrap">
        {!show && <View className="lunch-mp-wrap">
          <Image lazyLoad
            className="top-image"
            src={getImageURL('Fkpomg6eRknXUagEs4CgwyM4yMPC', '?imageslim')}
            mode="widthFix"
          />
          <View className="lunch-mp-name">即将跳转到{name}</View>
          <Button type="primary" block onClick={this.toWeapp.bind(this)}>
            打开{name}小程序
          </Button>
        </View>}
        <VDialog
          id="vanDialog3"
          title="提示"
          showCancelButton
          closeOnClickOverlay={false}
          show={show}
          beforeClose={this.beforeClose.bind(this)}
        >
          <View className="content">
            <View className="p">该服务由第三方提供</View>
            <View className="authorize">
              <View className="p">{name} 申请获取以下信息为您提供服务</View>
              <Text>获取你的登录手机号</Text>
            </View>
            <View className="agree">
              <Checkbox
                value={checked}
                checkedColor="#FD4925"
                onChange={this.handleChange.bind(this)}
              >我已阅读并同意</Checkbox>
              <Text className="agreement" onClick={this.toPage.bind(this)}>《用户协议》</Text>
            </View>
          </View>
        </VDialog>
        <Toast id="vanToast" zIndex={2000} />
      </View>

    )
  }
}
