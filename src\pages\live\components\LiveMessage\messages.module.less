.scrollContainer{
  flex: 1;
  overflow: hidden;
}
.scrollContainerContent {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100% !important;
  justify-content: space-between;
  background-color: #f5f5f5;
  box-sizing: border-box;
  padding-bottom: 95px;
  & >.waitingCount{
    height: 50px;
    font-size: 26px;
    line-height: 50px;
    padding-left: 20px;
    background-color: #fcf6ed;
    color: #de8c17;
  }
  .channelTitle{
    padding: 15px;
    background: #fff;
    display: flex;
    align-items: center;
    .channelTitleTag{
      background: #f06868;
      color: #ffffff;
      padding: 4px;
      font-size: 28px;
      margin-right: 6px;
      border-radius: 8px;
    }
    .channelTitleText{
      font-size: 32px;
    }
  }
  & >.messageContent {
    // height: 100%;
    flex: 1;
    // display: flex;
    overflow: hidden;
    > scroll-view{
      height: 100%;
    }
  }
  .loading{
    position: relative;
    padding: 30px;
  }
  .notice{
    text-align: center;
    padding: 10px 0;
    width: 100%;
    line-height: 50px;
    font-size: 30px;
    color: #333333;
  }
}
.inputWrapFixed{
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}
