import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { View, Text, Image } from '@tarojs/components'
import './index.scss'
import { login } from '@/tools';
import { getLoggedIn } from '@/gdata';

import AMap from '../../tools/amap-wx'

interface Props {
  avage: string
}

interface State {
  city: string,
  weather: string,
  temperature: string,
}

function formatCityName(city = '') {
  return city;
  // return city ? city.replace('市', '') : city;
}

export default class Index extends Component<Props, State> {


  // config: Config = {
  //   navigationBarTitleText: '用户头像天气组件'
  // }

  constructor(props) {
    super(props);

    this.state = {
      city: '郑州市',
      weather: '',
      temperature: ''
    };
  }


  // 获取天气
  getWeather() {
    const AMAP_KEY = '7cf8afd9ccf52328f502624c0365401c';
    let myAmapFun = new AMap.AMapWX({ key: AMAP_KEY });
    myAmapFun.getWeather({
      city: '郑州市',
      success: (res) => {
        // 成功回调
        this.setState({
          city: formatCityName(res.city.data),
          weather: res.weather.data,
          temperature: `${res.temperature.data}°`,
        });
      },
      fail: function(info){
        // 失败回调
        console.log(info)
      }
    })
  }
  componentDidMount () {

    // 获取天气信息
    this.getWeather();
  }

  componentWillUnmount () { }

  componentDidShow () {  }
  onClickAvatar(e) {
    // console.log(e);
    const isLoggedIn = getLoggedIn();
    if (!isLoggedIn) {
      login();
    }
  }
  componentDidHide () { }

  render () {
    let { city, weather, temperature } = this.state;
    let { avage } = this.props;

    return (
      <View className="city-wrap" onClick={this.onClickAvatar}>
        <Image className="ava" src={avage ? avage : `https://img.jgrm.net/Fr9WEh0tOeRea8vZjSgXgr2_K0By`} />
        <View className="weat">
          <View className="city">{city}</View>
          <View className="w-c">{weather} {temperature}</View>
        </View>
      </View>
    )
  }
}
