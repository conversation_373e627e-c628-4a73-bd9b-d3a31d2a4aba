page {
  background: #eeeff3;
}
.item-cell {
  display: flex;
  .item-cell__bd {
    flex: 1;
  }
  .vcode {
    margin: 0 15px;
    width: 150px;
    text-align: center;
    color: #EF7031;
    &.disabled {
      color: rgb(160, 160, 160);
    }
  }
  .input {
    width: 100%;
  }
}
.top {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 30px;

  .img {
    height: 200px;
    width: 200px;
    margin: 0 auto;
    display: block;
  }

  .title {
    font-size: 36px;
    font-weight: bold;
  }
}

.content {
  background: #fff;

  .item-input {
    font-size: 32px;
    position: relative;
    background-repeat: no-repeat;
    background-size: 45px 45px;
    background-position: 20px center;
    height: 110px;
    line-height: 110px;
    border-bottom: 1px solid rgb(221, 221, 221);
    padding-left: 30px;
    /* &.phone {
      background-image: url(./images/phoneImg.png);
    }
    &.security {
      background-image: url(./images/securityCode.png);
    }

    &.password {
      background-image: url(./images/password.png);
    } */

    .input {
      display: inline-block;
      vertical-align: middle;
      // &.tel {
      //   width:100%;
      //   padding-right:290rpx;
      //   box-sizing:border-box;
      // }
    }
  }

  .verify {
    position: absolute;
    right: 10px;
    top: 16px;
    height: 84px;
    line-height: 84px;
    background: #388FFF;
    color: #fff;
    width: 260px;
    font-size: 26px;

    &.send {
      background: #ccc;
    }
  }

  .tip {
    font-size: 24px;
    background: #EEEFF3;
    padding: 8px 20px;
    color: gray;
    .tip-alt {
      color: #268abb
    }
  }
}

.page-center {
  display: flex;
  align-items: center;
  flex-direction: column;
  height: 70vh;
  justify-content: center;
}
.login-tip {
  font-size: 24px;
  color: rgb(83, 83, 83);
  margin-top: 5px;
}
.btn {
  width: 80%;
  height: 80px;
  color: white;
  line-height: 80px;
  margin-top: 50px;
  margin-left: auto;
  margin-right: auto;
  background: #388FFF;
  background: #f1651e;
  font-size: 16PX;
  border: 0;
}
.btn-default {
  background: rgb(240, 240, 240);
  color: black;
}
.btn-chezhuka-login {
  margin: 30vh 20px 0;
  background: #388FFF;
  color: white;
}
.logo {
  background: url(./images/jglh-logo-2020.png) center center no-repeat;
  width: 200px;
  height: 200px;
  background-size: cover;
}
.link {
  font-size: 14PX;
  color: #477090;;
}
.link-areas {
  margin: 20px;
  text-align: center;
}
.btn-wrap {
  height: 50vh;
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.getUserBtn {
  width:80%;
  height: auto;
  color:rgba(255, 255, 255, 0.9);
  background:#398fff;
  border: none;
}
