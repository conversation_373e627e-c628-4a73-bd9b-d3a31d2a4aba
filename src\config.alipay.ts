export default {
  pages: [
    "pages/index/index",
    "pages/index/landing",
    "pages/shop/index",
    "pages/shop/shop",
    "pages/user/index",
    "pages/web/index",
    "pages/order/index",
    "pages/order/detail",
    "pages/login/index",
    "pages/payment-ali/index",
    "pages/platform/order",
  ],
  window: {
    backgroundTextStyle: "light",
    // navigationBarBackgroundColor: '#FF5925',
    navigationBarBackgroundColor: "#fff",
    navigationBarTitleText: "交广领航",
    navigationBarTextStyle: "black",
    navigationStyle: "default",
  },
  tabBar: {
    custom: false,
    selectedColor: "#000",
    borderStyle: "white",
    list: [
      {
        pagePath: "pages/index/index",
        text: "首页",
        iconPath: "./images/v2/icon-home.png",
        selectedIconPath: "./images/v2/icon-home-active.png",
      },
      // {
      //   pagePath: 'pages/shop/index',
      //   text: '商城',
      //   iconPath: './images/v2/icon-mall.png',
      //   selectedIconPath: './images/v2/icon-mall-active.png'
      // },
      {
        pagePath: "pages/user/index",
        text: "我的",
        iconPath: "./images/v2/icon-mine.png",
        selectedIconPath: "./images/v2/icon-mine-active.png",
      },
    ],
  },
  plugins: {},
  requiredPrivateInfos: ["getLocation"],
  permission: {
    "scope.userLocation": {
      desc: "天气显示自动切换地址",
    },
  },
  requiredBackgroundModes: ["audio"],
  usePrivacyCheck: true,
};
