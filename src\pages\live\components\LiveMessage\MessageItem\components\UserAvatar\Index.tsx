import React from 'react'
import Taro from "@tarojs/taro";
import { Image, View } from "@tarojs/components";
import { getImageURL } from "@/util";
import DefaultAvatar from '@/images/avatar.png'
import styles from './index.module.less'

const env = Taro.getEnv()

const Index: React.FC<{
  message: APP.Message
}> = props => {
  // let avatar = <Image src={DefaultAvatar} className={styles.messageAvatar}  />
  // switch (env) {
  //   case Taro.ENV_TYPE.WEAPP:
  //     avatar = <View className={styles.messageAvatar}>
  //       <OpenData type="userAvatarUrl" />
  //     </View>
  //   default: {
  //     avatar = (props.message.sender?.portrait && !props.message.sender.portrait.startsWith('/storage/')) ? <Image src={getImageURL(props.message.sender.portrait)} className={styles.messageAvatar} /> :
  //     <Image src={DefaultAvatar} className={styles.messageAvatar}  />
  //     // avatar = <Image src={getImageURL(props.message.imgUrl)} className={styles.messageAvatar} />
  //   }
  // }
  let avatar = (props.message.sender?.portrait && !props.message.sender.portrait.startsWith('/storage/')) ? <Image src={getImageURL(props.message.sender.portrait)} className={styles.messageAvatar} /> :
  <Image src={DefaultAvatar} className={styles.messageAvatar}  />
  return avatar
}
export default Index
