import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { View, Image, Text, Swiper, SwiperItem } from '@tarojs/components'
import { getFullImagePath } from '../../../../tools'

import './index.scss'


interface Menu {
  id: number
  sharecontent: any
  shareurl: any
  superscriptFlag: number
  superscriptImage: string
  thumb: string
  title: string
  typeflag: number
  url: string
}

interface Props {
  menulist: Menu[],
  menuBg: string,
  toPage: Function
}

interface State {
  current: number
}

export default class ActionList extends Component<Props, State> {


  // config: PageConfig = {
  //   navigationBarTitleText: '首页',
  // }

  constructor(props) {
    super(props);

    this.state = {
      current: 0
    };
  }

  componentWillMount () {
  }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidShow () {


  }

  componentDidHide () { }
  toPage(url:string) {
    this.props.toPage(url);
  }

  changeEnd(ev) {
    this.setState({
      current: ev.currentTarget.current
    });
  }


  render () {
    let { menulist, menuBg } = this.props;

    let menuArr:any = [];
    let tmp = menulist;
    let key = -1;
    if (!tmp) {
      tmp = [];
    }
    for (var i = 0, len = tmp.length; i < len; i++) {
      if (i % 5 === 0) {
        key ++;
      }
      if (!menuArr[key]) {
        menuArr[key] = [];
      }
      menuArr[key].push(tmp[i]);
    }
    const menuListDot = menuArr.map((item, index) => {
      return <View key={item.id}  className={['dot', index === this.state.current ? 'this' : null].join()} />
    });
    // 按钮图标
  const menuList = menuArr.map((item:any) => {
      return <SwiperItem>
        {item.map((item2:any) => {
          return <View className="menu-item" key={item2.id} onClick={this.toPage.bind(this, item2.url)}>
                  <View className="menu-item-img">
                    <Image src={getFullImagePath(item2.thumb)} className="img" />
                  </View>
                  {
                    item2.superscriptFlag === 1 && <Image src={getFullImagePath(item2.superscriptImage)} className="brdige" />
                  }
                  <Text className="menu-item-text">{item2.title}</Text>
                </View>
        })}
      </SwiperItem>
    });
    return (
      <View className="menu" style={`background-image: url(${getFullImagePath(menuBg)});`}>
        <Swiper
          className="menu-swuper"
          onChange={this.changeEnd.bind(this)}
        >
          {menuList}
        </Swiper>
        <View className="dots">
          {menuListDot}
        </View>
      </View>
    )
  }
}
