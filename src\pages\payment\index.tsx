import { setWebViewCallbackURL } from '@/gdata';
import { showModal, showToast } from '@/tools';
import { View } from '@tarojs/components';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import React, { Component }  from 'react'
import { OrderCategory } from '@/enums';
import { filterSubscribeMessage } from "@/util";
import { getOrderInfo, getXcxMessages, getXcxMessagesV2 } from "../../api/modules/user";
import './index.scss';


interface paymentOptionProps {
  timeStamp: any,
  nonceStr: any,
  package: any,
  signType: any,
  paySign: any,
  orderInfo?: any,
}

// namespace wx{
//   let requestOrderPayment: any
// }
// interface wx {
//   requestOrderPayment: any,
// }
// 当前版本taro中不包含 requestOrderPayment ，此处包装为 Promise 形式
function requestOrderPayment(options) {
  return new Promise((resolve, reject) => {
    const paymentOptions = Object.assign({}, options, {})
    paymentOptions.success = function(res) {
      resolve(res);
    }
    paymentOptions.fail = function(err) {
      reject(err);
    }
    Taro.requestOrderPayment(paymentOptions)
  })
}

export default class Index extends Component {
  $instance: any = getCurrentInstance()
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentWillMount () {
    let successURL = decodeURIComponent(this.$instance.router.params.success);
    let failURL = decodeURIComponent(this.$instance.router.params.fail);
    let category = decodeURIComponent(this.$instance.router.params.category); // 订单类型
    // console.log('componentWillMount: ', this.$instance.router.params)
    // console.log('window.wx: ', wx);
    const options = Taro.getLaunchOptionsSync();
    getOrderInfo(this.$instance.router.params.orderId).then(res => {
      // Taro.requestOrderPayment();
      const paymentOptions:paymentOptionProps = {
        'timeStamp': res.timeStamp,
        'nonceStr': res.nonceStr,
        'package': res.package,
        'signType': res.signType,
        'paySign': res.paySign,
      }
      if (res.orderInfo) {
        paymentOptions.orderInfo = res.orderInfo;
        if (!('scene' in res.orderInfo)) {
          paymentOptions.orderInfo.scene = options.scene;
        }
        // paymentOptions.order_info.scene = options.scene;
      }
      console.log('paymentOptions:', paymentOptions);
      // 2021年6月23日16:28:40，当前版本 taro 下无requestOrderPayment方法，暂时使用 wx 对象调用
      return requestOrderPayment(paymentOptions).then((payres) => {
        getXcxMessagesV2().then(serviceArr => {
          let subs: any = filterSubscribeMessage(serviceArr, category || '')
          if (subs.length) {
            Taro.requestSubscribeMessage({
              tmplIds: subs,
              entityIds:[], // 解决ts报错，请忽略 模板小程序 appId，仅在服务商代调用场景下需要传入
              complete: function(success) {
                // 设置临时的回调URL
                setWebViewCallbackURL(successURL);
                setTimeout(function() {
                  Taro.navigateBack();
                }, 30);
              },
              success: function(success) {
                console.log("订阅成功", success);
              },
              fail: function(err) {
                console.log("订阅失败", err);
              }
            });
            return
          }
          // 设置临时的回调URL
          setWebViewCallbackURL(successURL);
          Taro.navigateBack();
        })
      }).catch(err => {
        // showToast('err' + err);
        console.log(err);
        setWebViewCallbackURL(failURL);
        showToast(err.errMsg);
        Taro.navigateBack();
        // showModal('提示', err.errMsg, {
        //   success: () => {
        //     Taro.navigateBack();
        //   }
        // })
      });
    }).catch(err => {
      showModal('提示', String(err), {
        showCancel: false,
        complete: () => {
          Taro.navigateBack();
        }
      })
    });
  }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidShow () {
  }

  componentDidHide () { }

  // config: Config = {
  //   navigationBarTitleText: '收银台'
  // }

  showError(e:string):void {
    console.log(e, 333);
  }
  loadFunishPage(e:string):void {
    console.log(e, 'nnn');
  }

  render () {
    return (
      <View className="page">
        正在请求支付...
      </View>
    )
  }
}
