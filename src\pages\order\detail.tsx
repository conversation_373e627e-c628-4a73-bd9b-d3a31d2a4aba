import Taro, { getCurrentInstance } from '@tarojs/taro'
import React, { Component }  from 'react'
import {  View } from '@tarojs/components'
import { WEBAPP_BASE } from '@/env/pages';
import { getWebViewURL, pushWebView } from '@/tools';
import { getLoggedIn } from '@/gdata';
import './detail.scss';

/**
 * 小程序大部分业务直接复用了 web端页面
 * 因直播带货业务需求，小程序 `我的订单` 入口中订单需要一个详情页面
 * 此页面接受两个参数：业务订单id和订单类型，前端根据订单类型跳转到对应的订单详情web页面
 */

// 系统订单类型枚举值
// car_inspection(审车)
// washcard-buy(养车卡)
// foods(美食)
// car-maintenance(保养)
// action(活动)
// car(洗车)
// memberNew(会员)
// group-buy(团购商城)
// shop(商城)

enum OrderType {
  GroupBuy ='group-buy',  // 领航商城
  Food = 'foods', // 美食商城
  Activity = 'action', // 报名活动
  CarWash = 'car', // 洗车
  CarMaintain = 'car-maintenance', // 养车
  WashCardOfCar = 'washcard-buy', // 洗车卡
  VehicleInspection = 'car_inspection', // 审车
}

type OrderPage = {
  [key in OrderType]?: ((value: string) => string);
};

interface State {
  url: string,
  params: any,
  loggedIn: boolean,
}
const OrderPages: OrderPage = {
  // 领航商城
  [OrderType.GroupBuy]: oid => `${WEBAPP_BASE}?nohead#/mall/order/${oid}`,
  // 洗车
  [OrderType.CarWash]: oid => `${WEBAPP_BASE}?nohead#/order/${oid}`,
  // 养车
  [OrderType.CarMaintain]: oid => `${WEBAPP_BASE}?nohead#/order/${oid}`,
  // 美食商城
  [OrderType.Food]: oid => `${WEBAPP_BASE}?nohead#/mallg2/order/${oid}`,
  // 车务
  [OrderType.VehicleInspection]: oid => `${WEBAPP_BASE}?nohead#/vehicle-business/order/${oid}`,
}

function getOrderPage(t: string, oid: string) {
  const getPage = OrderPages[t];
  return getPage ? getPage(oid) : null;
}

export default class Index extends Component<any, State> {

  // config: Config = {
  //   navigationBarTitleText: '订单详情'
  // }
  $instance: any = getCurrentInstance()

  constructor(props) {
    super(props);
    this.state = {
      url: '',
      params: {},
      loggedIn: false,
    };
  }

  componentWillMount () {
    const loggedIn = getLoggedIn();
    const params = this.$instance.router.params;
    const oid = params.oid; // 业务订单号
    const type = params.type; // 系统订单类型
    const url = getOrderPage(type, oid)
    if (url) {
      pushWebView(url, {
        replace: true,
        requireSignIn: true,
      })
    }
    this.setState({
      params: params,
      url: url,
      loggedIn,
    })
  }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidHide () {}


  onError(e) {
    console.error(e);
  }

  onLoad(e) {
    console.log(e);
  }

  render () {
    // console.log(this.state.toUrl, '55');
    const params = this.state.params;
    const url = this.state.url;
    return (
      <View className="page">
        {!url && <View>
          <View>不支持的订单类型</View>
          <View>{params.type}：{params.oid}</View>
        </View> }
      </View>
    )
  }
}
