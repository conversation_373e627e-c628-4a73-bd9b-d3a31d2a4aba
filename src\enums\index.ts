export enum UserType {
  Jglh = "jglh",
  CheZhuKa = "chezhuka",
}

// 第三方业务，从其他小程序跳转而来的
export enum ThirdPartBiz {
  // 2019年11月，车主卡跳转而来的，洗车下单享受vip价格
  CheZhuKaVip = "jglh-czk-2019",
}

// 订单类型
export enum OrderCategory {
  CAR_LIFE = "cl", // ['车生活订单',  '车生活订单'],
  MALL = "mall", // ['团购订单',  '团购订单'],
  INSPECTION = "inspection", // ['审车订单',  '审车订单'],
  FUELCARD = "fuelcard", // ['加油卡预存',  '我的加油卡预存'],
  FUELCARDRECHARGE = "fuelcardrecharge", // ['加油卡预存',  '我的加油卡预存'],
  VIP = "vip", // ['交广领航VIP会员订单',  '交广领航VIP会员订单'],
  CAR_WASH_CARD = "washcard", // ['洗车卡',  '交广领航洗车卡'],
}
export enum liveModeType {
  NORMAL = 1, // 正常直播
  STOP = -1, // 直播结束
}
