import React, { ReactElement, useCallback, useEffect, useState } from "react";
import Taro from "@tarojs/taro";
import { Video, View, LivePlayer, Block, ScrollView } from "@tarojs/components";
import { Icon } from "@antmjs/vantui";
// import { getLiveRoomList } from "@/api/modules/chat";
import { liveModeType } from "@/enums";
import { pushWebView } from "@/tools";
import "../../live.scss";
import styles from "./video.module.less";

interface Props {
  rooms: Array<APP.LiveRoom>;
  liveRoom: APP.LiveRoom | undefined;
  env: string;
  onlineText: string;
  liveModeChangeMsg: APP.Message;
  onSwitchRoom: (value: APP.LiveRoom) => void;
}

// 房主
const LiveVideo = ({
  rooms,
  liveRoom,
  env,
  onlineText,
  liveModeChangeMsg,
  onSwitchRoom,
}: Props): ReactElement => {
  // const [rooms, setRooms] = useState<Array<APP.LiveRoom>>([]);
  // const [liveVideoContext, setLiveVideoContext] = React.useState<any>();
  // const [videoContext, setVideoContext] = React.useState<any>();
  const [playUrl, setPlayUrl] = useState<string>("");
  const [playing, setPlaying] = useState<boolean>(false);
  const [showCover, setShowCover] = useState<boolean>(true);
  const [channel, setChannel] = useState<boolean>(true);
  const [fullScreen, setFullScreen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [isRtmp, setIsRtmp] = useState<boolean>(true);
  const [videoKey, setVideoKey] = useState<number>(888);

  const coverTimer = React.useRef<any>();

  const channelTimer = React.useRef<any>();

  const liveModeTimer = React.useRef<any>();

  const liveVideoRef = React.useRef<any>();

  const toPage = useCallback((url: string | undefined) => {
    if (!url) {
      return;
    }
    pushWebView(url);
  }, []);

  const handleChannelTouchStart = useCallback(() => {
    clearTimeout(channelTimer.current);
  }, []);

  const handleChannelTouchMove = useCallback(() => {
    clearTimeout(channelTimer.current);
  }, []);

  const handleChannelTouchEnd = useCallback(() => {
    playing &&
      (channelTimer.current = setTimeout(() => {
        setChannel(false);
      }, 3000));
  }, [playing]);

  const handleTapPlayer = useCallback(() => {
    if (!playing) {
      setShowCover(true);
      setChannel(true);
      return;
    }
    if (showCover) {
      setShowCover(false);
      setChannel(false);
    } else {
      clearTimeout(coverTimer.current);
      setShowCover(true);
      setChannel(true);
      coverTimer.current = setTimeout(() => {
        setShowCover(false);
      }, 3000);
      // clearTimeout(coverTimer.current);
      // clearTimeout(channelTimer.current);
    }
  }, [playing, showCover]);

  const handlePlay = useCallback(
    (e) => {
      // console.log("开始");
      e.stopPropagation();
      if (liveModeChangeMsg?.liveMode === liveModeType.STOP) {
        Taro.showToast({
          title: "直播已结束",
          icon: "none",
        });
        toPage(liveModeChangeMsg?.liveOfflineUrl);
        return;
      }
      liveRoom && setPlayUrl(liveRoom.liveVideoUrl);
      liveVideoRef.current?.play({
        success: () => {
          setPlaying(true);
        },
        fail: (err) => {
          console.log(err);
        },
        complete: () => {
          // console.log("开始complete", err);
        },
      });
    },
    [liveModeChangeMsg, toPage, liveRoom]
  );

  const handlePause = useCallback((e?) => {
    e && e.stopPropagation();
    // console.log("暂停");
    liveVideoRef.current?.stop({
      success: () => {
        setPlaying(false);
        setShowCover(true);
        setChannel(true);
      },
      fail: (err) => {
        console.log(err);
      },
      complete: () => {
        // console.log("暂停complete", err);
        setPlayUrl("");
        setVideoKey(Math.random() * 1000);
      },
    });
  }, []);

  const handleFullScreen = useCallback((e) => {
    e.stopPropagation();
    console.log("全屏", liveVideoRef.current);
    liveVideoRef.current?.requestFullScreen({
      direction: 90,
      success: () => {
        // console.log("全屏成功");
      },
      fail: (err) => {
        console.log("全屏失败", err);
      },
    });
  }, []);

  const handleExitFullScreen = useCallback((e) => {
    e.stopPropagation();
    // console.log("退出全屏");
    liveVideoRef.current?.exitFullScreen();
  }, []);

  const handleFullScreenChange = useCallback((e) => {
    const { fullScreen } = e.detail;
    if (!fullScreen) {
      setFullScreen(false);
    } else {
      setFullScreen(true);
    }
  }, []);

  const selectRoom = useCallback(
    (room, e) => {
      e.stopPropagation();
      onSwitchRoom(room);
    },
    [onSwitchRoom]
  );

  const handleVideoPlay = useCallback(
    (e?) => {
      if (!playing) {
        // console.log("开始");
        e && e.stopPropagation();
        if (liveModeChangeMsg?.liveMode === liveModeType.STOP) {
          Taro.showToast({
            title: "直播已结束",
            icon: "none",
          });
          toPage(liveModeChangeMsg?.liveOfflineUrl);
          return;
        }
        liveRoom && setPlayUrl(liveRoom.liveVideoUrl);
        liveVideoRef.current?.play();
        setPlaying(true);
        setShowCover(false);
        setChannel(false);
      }
    },
    [playing, liveModeChangeMsg, liveRoom, toPage]
  );

  const handleVideoPause = useCallback((e?) => {
    // console.log("暂停");
    e && e.stopPropagation();
    liveVideoRef.current?.stop();
    clearTimeout(coverTimer.current);
    setPlaying(false);
    setShowCover(true);
    setChannel(true);
    setPlayUrl("");
    setVideoKey(Math.random() * 1000);
  }, []);

  const handleVideoFullScreen = useCallback((e) => {
    // console.log("全屏");
    e.stopPropagation();
    liveVideoRef.current?.requestFullScreen({
      direction: 90,
    });
  }, []);

  const handleVideoExitFullScreen = useCallback((e) => {
    // console.log("退出全屏");
    e.stopPropagation();
    liveVideoRef.current?.exitFullScreen();
  }, []);

  const handleVideoError = useCallback(() => {
    clearTimeout(coverTimer.current);
    clearTimeout(channelTimer.current);
    setPlaying(false);
    setShowCover(true);
    setChannel(true);
  }, []);
  const handleStateChange = useCallback(
    (e) => {
      console.log(e.detail);
      if (
        e.detail.code === 2006 ||
        e.detail.code === -2301 ||
        e.detail.code === 3002
      ) {
        clearTimeout(coverTimer.current);
        clearTimeout(channelTimer.current);
        setPlaying(false);
        setShowCover(true);
        setChannel(true);
        if (e.detail.code === 2006) {
          Taro.showToast({
            title: "视频播放结束",
            icon: "none",
          });
        } else if (e.detail.code === -2301) {
          Taro.showToast({
            title: "网络断连,请检查网络后重试",
            icon: "none",
          });
        } else if (e.detail.code === 3002) {
          Taro.showToast({
            title: "RTMP服务器连接失败,请稍后重试",
            icon: "none",
          });
        }
      } else if (
        e.detail.code === 2001 ||
        e.detail.code === 2002 ||
        e.detail.code === 2003
      ) {
        setLoading(true);
      } else if (e.detail.code === 2004) {
        if (liveModeChangeMsg?.liveMode === liveModeType.NORMAL) {
          setPlaying(true);
        }
        setShowCover(false);
        setChannel(false);
      }
    },
    [liveModeChangeMsg]
  );

  useEffect(() => {
    if (liveRoom?.liveVideoUrl) {
      setPlayUrl(liveRoom.liveVideoUrl);
      setVideoKey(Math.random() * 1000);
      // debugger
      // 正则表达式匹配 .m3u8，考虑到可能有参数在后面
      const rtmpPattern = /^rtmp:\/\//i;
      if (rtmpPattern.test(liveRoom.liveVideoUrl)) {
        const innerVidioText = Taro.createLivePlayerContext("livePlayer");
        liveVideoRef.current = innerVidioText;
        setIsRtmp(true);
      } else {
        const innerVidioText = Taro.createVideoContext("videoPlayer");
        setShowCover(false);
        setPlaying(true);
        setChannel(false);
        liveVideoRef.current = innerVidioText;
        setIsRtmp(false);
      }
    }
    return () => {
      liveVideoRef.current?.stop();
      setPlaying(false);
      setShowCover(true);
      setChannel(true);
    };
  }, [liveRoom]);

  useEffect(() => {
    if (playing) {
      coverTimer.current = setTimeout(() => {
        setShowCover(false);
      }, 3000);
    }
    return () => {
      clearTimeout(coverTimer.current);
      // clearTimeout(channelTimer.current);
    };
  }, [playing]);

  useEffect(() => {
    if (playing && channel) {
      channelTimer.current = setTimeout(() => {
        setChannel(false);
      }, 3000);
    }
    return () => {
      clearTimeout(channelTimer.current);
    };
  }, [playing, channel]);

  useEffect(() => {
    if (liveModeChangeMsg?.liveMode === liveModeType.STOP) {
      if (liveModeTimer.current) {
        clearTimeout(liveModeTimer.current);
      }
      handlePause();
      handleVideoPause();
      liveModeTimer.current = setTimeout(() => {
        handlePause();
        handleVideoPause();
        setPlayUrl("");
        setVideoKey(Math.random() * 1000);
        toPage(liveModeChangeMsg?.liveOfflineUrl);
      }, 500);
    }
    return () => {
      clearTimeout(liveModeTimer.current);
    };
  }, [handlePause, handleVideoPause, liveModeChangeMsg, toPage]);

  return (
    <View className={styles.liveView}>
      {isRtmp && (
        <LivePlayer
          id="livePlayer"
          className={styles.video}
          src={liveModeChangeMsg?.liveMode === liveModeType.STOP ? "" : playUrl}
          key={videoKey}
          mode="live"
          autoplay
          autoPauseIfNavigate={false}
          onStateChange={handleStateChange}
          onFullScreenChange={handleFullScreenChange}
        >
          {/* </Block> */}
          <View className={styles.liveControlWrap} onClick={handleTapPlayer}>
            {/* 多少人看过 */}
            <Block>
              {!!onlineText && (
                <View className={styles.onlineCount}>
                  <View>{onlineText}</View>
                </View>
              )}
              {/* 暂停时海报 */}
              {!playing && <View className={styles.livePoster} />}
              {showCover && (
                <View className={styles.liveControl}>
                  {!playing && (
                    <Icon
                      className={styles.playBtn}
                      name="play-circle-o"
                      size="40px"
                      color="#ffffff"
                      onClick={handlePlay}
                    />
                  )}
                  {playing && (
                    <Icon
                      className={styles.playBtn}
                      name="pause-circle-o"
                      size="40px"
                      color="#ffffff"
                      onClick={handlePause}
                    />
                  )}
                  {playing && !fullScreen && (
                    <Icon
                      className={styles.fullScreen}
                      name="expand-o"
                      size="28px"
                      color="#ffffff"
                      onClick={handleFullScreen}
                    />
                  )}
                  {fullScreen && (
                    <Icon
                      className={styles.fullScreen}
                      name="shrink"
                      size="28px"
                      color="#ffffff"
                      onClick={handleExitFullScreen}
                    />
                  )}
                </View>
              )}
            </Block>
            {/* 频率列表 */}
            {channel && (
              <ScrollView
                scrollX
                enableFlex
                enhanced
                showScrollbar={false}
                scrollIntoView={`channel_${liveRoom?.id}`}
                className={styles.channel}
                onTouchStart={handleChannelTouchStart}
                onTouchMove={handleChannelTouchMove}
                onTouchEnd={handleChannelTouchEnd}
              >
                {rooms.map((v) => {
                  return (
                    <View
                      id={`channel_${v.id}`}
                      className={`${styles.channelItem} ${
                        v.id == liveRoom?.id ? styles.active : ""
                      }`}
                      key={v.id}
                      onClick={(e) => {
                        selectRoom(v, e);
                      }}
                    >
                      {v.name}
                    </View>
                  );
                })}
              </ScrollView>
            )}
          </View>
        </LivePlayer>
      )}
      {!isRtmp && (
        <Video
          id="videoPlayer"
          className={styles.video}
          src={liveModeChangeMsg?.liveMode === liveModeType.STOP ? "" : playUrl}
          key={videoKey}
          controls={false}
          autoplay
          isLive
          autoPauseIfNavigate={false}
          // onStateChange={handleStateChange}
          // onPlay={handleVideoPlay}
          // onPause={handleVideoPause}
          // // onLoading={handlePlay}
          onError={handleVideoError}
          onFullScreenChange={handleFullScreenChange}
        >
          {/* </Block> */}
          <View className={styles.liveControlWrap} onClick={handleTapPlayer}>
            <Block>
              {!!onlineText && (
                <View className={styles.onlineCount}>
                  <View>{onlineText}</View>
                </View>
              )}
              {!playing && <View className={styles.livePoster} />}
              {showCover && (
                <View className={styles.liveControl}>
                  {/* <Block> */}
                  {!playing && (
                    <Icon
                      className={styles.playBtn}
                      name="play-circle-o"
                      size="40px"
                      color="#ffffff"
                      onClick={handleVideoPlay}
                    />
                  )}
                  {playing && (
                    <Icon
                      className={styles.playBtn}
                      name="pause-circle-o"
                      size="40px"
                      color="#ffffff"
                      onClick={handleVideoPause}
                    />
                  )}
                  {playing && !fullScreen && (
                    <Icon
                      className={styles.fullScreen}
                      name="expand-o"
                      size="28px"
                      color="#ffffff"
                      onClick={handleVideoFullScreen}
                    />
                  )}
                  {fullScreen && (
                    <Icon
                      className={styles.fullScreen}
                      name="shrink"
                      size="28px"
                      color="#ffffff"
                      onClick={handleVideoExitFullScreen}
                    />
                  )}
                </View>
              )}
            </Block>

            {channel && (
              <ScrollView
                scrollX
                enableFlex
                enhanced
                showScrollbar={false}
                scrollIntoView={`channel_${liveRoom?.id}`}
                className={styles.channel}
                onTouchStart={handleChannelTouchStart}
                onTouchMove={handleChannelTouchMove}
                onTouchEnd={handleChannelTouchEnd}
              >
                {rooms.map((v) => {
                  return (
                    <View
                      id={`channel_${v.id}`}
                      className={`${styles.channelItem} ${
                        v.id == liveRoom?.id ? styles.active : ""
                      }`}
                      key={v.id}
                      onClick={(e) => {
                        selectRoom(v, e);
                      }}
                    >
                      {v.name}
                    </View>
                  );
                })}
              </ScrollView>
            )}
            {/* </Block> */}
          </View>
        </Video>
      )}
    </View>
  );
};

export default React.memo(LiveVideo);
