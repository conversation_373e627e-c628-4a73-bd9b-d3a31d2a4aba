.prize-page {
  background-color: #f1f1f1;
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: 20px;
  -webkit-overflow-scrolling: touch;
}
.prize-header {
  margin-bottom: 20px;
  padding: 40px;
  background: linear-gradient(to bottom, #8772eb, #ffffff);
}

.prize-info {
  margin: 20px auto 0;
}

.prize-name {
  font-size: 36px;
  display: block;
  margin-bottom: 10px;
}

.prize-description {
  font-size: 28px;
  color: #888;
}

.prize-rules {
  margin-bottom: 20px;
  padding: 40px;
  background-color: #fff;
}

.rules-title {
  font-size: 28px;
  color: #999999;
  display: block;
  margin-bottom: 20px;
}

.rules-description {
  // font-size: 30px;
  color: #111111;
  display: block;
  overflow: hidden;
}

.btn-wrap {
  // background: #fff;
  padding: 30px;
  // 固定在底部
  // position: fixed;
  // bottom: 30px;
  // left: 16px;
  // right: 16px;
  // z-index: 100;
  .prize-btn {
    width: 100%;
    padding: 30px;
    box-sizing: border-box;
    background-color: #ff4500;
    color: #fff;
  }
}

.van-button--danger {
  background-color: #ce4e41;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}
