import React from "react";
import { View, Image, Text } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { isValidURL } from "@/util";
import { pushWebView } from "@/tools";
import styles from "../index.module.less";

type Articles = {
  item: Array<ArticlesItem>;
};

type ArticlesItem = {
  OpenType: number;
  Description: string;
  Title?: string;
  Url: string;
  PicUrl: string;
};

interface Props {
  Articles: Articles;
  CreateTime: number;
  ArticleCount?: string;
  MsgType: string;
  Content?: string;
}

const Index: React.FC<{
  content: Props;
}> = (props) => {
  // const item: APP.NavigatorContent = JSON.parse(props.content)
  console.log(props);
  const preview = React.useCallback((url: string) => {
    Taro.previewImage({
      urls: [url],
    }).then();
  }, []);
  const goto = React.useCallback((url) => {
    if (isValidURL(url)) {
      pushWebView(url);
      // pushWebView(Pages.USER_SIGN, {
      //   share: false,
      //   // requireSignIn: true,
      // });
    } else {
      Taro.navigateTo({
        url,
      }).catch(() => {
        Taro.showToast({
          icon: "none",
          title: "路径不存在",
        });
      });
    }
  }, []);

  return React.useMemo(() => {
    return (
      <View className={styles.replyContent}>
        {/* 文本 */}
        {props.content.MsgType == "text" && (
          <View>{props.content.Content}</View>
        )}
        {/* 图片 */}
        {props.content.MsgType == "image" && (
          // <View onClick={() => goto(props.content.Articles?.item[0]?.Url)}>
          <Image
            className={styles.image}
            src={props.content.Articles?.item[0]?.PicUrl}
            mode="widthFix"
            lazyLoad
            onClick={() => preview(props.content.Articles?.item[0]?.PicUrl)}
          />
          // </View>
        )}
        {/* 图文 */}
        {props.content.MsgType == "news" &&
          props.content.Articles?.item?.map((v, i) => {
            return (
              <View
                key={i}
                onClick={() => goto(v.Url)}
                className={styles.newsItem}
              >
                {i === 0 && (
                  <View className={styles.firstNews}>
                    <Image
                      className={styles.image}
                      src={v.PicUrl}
                      mode="widthFix"
                      lazyLoad
                    />
                    <View className={styles.title}>{v.Title}</View>
                  </View>
                )}
                {i != 0 && (
                  <View className={styles.restNews}>
                    <View className={styles.title}>{v.Title}</View>
                    <Image
                      className={styles.image}
                      src={v.PicUrl}
                      mode="widthFix"
                      lazyLoad
                    />
                  </View>
                )}
              </View>
            );
          })}
      </View>
    );
  }, [
    goto,
    preview,
    props.content.Articles?.item,
    props.content.Content,
    props.content.MsgType,
  ]);
};
export default Index;
