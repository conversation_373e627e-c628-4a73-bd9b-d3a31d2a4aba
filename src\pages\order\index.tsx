import Taro, { getCurrentInstance } from '@tarojs/taro'
import React, { Component }  from 'react'
import {  View } from '@tarojs/components'



interface State {
  params: any,
}
export default class Index extends Component<any, State> {

  // config: Config = {
  //   navigationBarTitleText: '订单'
  // }
  $instance: any = getCurrentInstance()
  constructor(props) {
    super(props);
    this.state = {
      params: {}
    };
  }

  componentWillMount () {
    const params = this.$instance.router.params;
    const oid = params.oid;
    const type = params.type;
    this.setState({
      params: params,
    })
  }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidHide () {}

  onError(e) {
    console.error(e);
  }

  onLoad(e) {
    console.log(e);
  }

  render () {
    // console.log(this.state.toUrl, '55');
    const params = this.state.params;
    return (
      <View>
        <View>{params.oid}</View>
        <View>{params.type}</View>
      </View>
    )
  }
}
