{
  "compilerOptions": {
    "target": "es2018",
    "module": "commonjs",
    "removeComments": true,
    "preserveConstEnums": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "noImplicitAny": false,
    "allowSyntheticDefaultImports": true,
    "outDir": "lib",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "strictNullChecks": true,
    "sourceMap": true,
    "baseUrl": ".",
    "rootDir": ".",
    "jsx": "preserve",
    "jsxFactory": "Taro.createElement",
    "allowJs": true,
    "resolveJsonModule": true,
    "typeRoots": [
      "node_modules/@types",
      "src/types"
    ],
    "paths": {
      "@": ["src"],
      "@/*": ["src/*"]
    },
  },
  "exclude": [
    "node_modules",
    "dist"
  ],
  "compileOnSave": false
}
