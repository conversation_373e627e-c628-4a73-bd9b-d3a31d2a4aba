import React from "react";
import { View, Input, Image, Button, Text } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { newAction } from "@/util/live";
import { getToken } from "@/util/auth";
import { getLoggedIn, setLoggedIn, setUserInfo, getSessionId } from "@/gdata";
import { initQiniuUploadToken } from "@/api/modules/chat";
import { getUserInfo } from "@/api/modules/user";
import { requestAuth } from "@/api/modules/auth";
import PictureImg from "@/images/picture.png";
import styles from "./index.module.less";
import context from "../context";

const Index: React.FC<{
  room: any;
  roomConfig: any;
  keyboardHeightChange: (value: string) => void;
}> = (props) => {
  const [value, setValue] = React.useState("");

  // const [templateId, setTemplateId] = React.useState("");

  // const [hadSubscribe, setHadSubscribe] = React.useState<boolean>(false);

  const [isIphonex, setIphonex] = React.useState(false);

  const [imgUploadToken, setImgUploadToken] = React.useState("");

  const [audioUploadConfig, setAudioUploadConfig] = React.useState<any>({});

  const [voice, setVoice] = React.useState(false);

  const [voiceLock, setVoiceLock] = React.useState(false);

  const [loginStatus, setLoginStatus] = React.useState(true);

  const [roomStatus, setRoomStatus] = React.useState("");

  const touch = React.useRef<any>({ startPoint: {} });
  // 创建定时器 ref 对象
  const timerRef = React.useRef<any>();

  type QiNiuResponse = {
    key: string;
    hash: string;
  };
  // 获取mesages.tsx提供的上下文
  const send = React.useContext(context);
  // 获取全局唯一的录音管理器 RecorderManager
  const recorderManager = Taro.getRecorderManager();
  // 初始化上传七牛token
  React.useEffect(() => {
    if (voice) {
      initQiniuUploadToken(2).then((res) => {
        setAudioUploadConfig(res);
      });
    }
  }, [voice]);
  React.useEffect(() => {
    // if (Taro.getEnv() === Taro.ENV_TYPE.WEAPP) {
    //   getTemplateId().then(res => {
    //     setTemplateId(res.data.id)initQiniuUploadToken
    //   })
    // }
    initQiniuUploadToken().then((token) => {
      setImgUploadToken(token);
    });
    initQiniuUploadToken(2).then((res) => {
      setAudioUploadConfig(res);
    });
  }, []);
  React.useEffect(() => {
    // requestAuth().then(getUserInfo).then(info => {
    requestAuth()
      .then(() => {
        // setUserInfo(info);
        setLoggedIn(true);
        setLoginStatus(true);
      })
      .catch((err) => {
        console.error(err);
        setLoggedIn(false);
        setLoginStatus(false);
      });
  }, [props.roomConfig]);
  // 发送图片
  const selectImg = React.useCallback(() => {
    Taro.chooseImage({}).then((res) => {
      res.tempFilePaths.forEach((path) => {
        Taro.uploadFile({
          url: UPLOAD_QINIU_URL,
          filePath: path,
          name: "file",
          formData: { token: imgUploadToken },
        }).then((r) => {
          if (send) {
            const result: QiNiuResponse = JSON.parse(r.data);
            if (result.key) {
              newAction({
                content: result.key,
                type: 2,
                toId: props.room?.roomId || "HN0001",
              }).then((act) => {
                send(act).then().catch();
              });
            }
          }
        });
      });
    });
  }, [imgUploadToken, send, props.room]);
  // 发送音频
  const uploadAudio = React.useCallback(
    (audio) => {
      Taro.uploadFile({
        url: UPLOAD_QINIU_URL,
        filePath: audio.tempFilePath,
        name: "file",
        formData: {
          token: audioUploadConfig.token,
          key: audioUploadConfig.rid,
        },
      }).then((r) => {
        if (send) {
          const result: QiNiuResponse = JSON.parse(r.data);
          // console.log("上传成功++", result);
          if (result.key) {
            newAction({
              content: result.key,
              type: 1,
              toId: props.room?.roomId || "HN0001",
              mtype: 2,
              speech: Math.ceil(audio.duration / 1000),
            }).then((act) => {
              send(act)
                .then(() => {
                  initQiniuUploadToken(2).then((res) => {
                    setAudioUploadConfig(res);
                  });
                })
                .catch();
            });
          }
        }
      });
    },
    [audioUploadConfig, send, props.room]
  );
  // 发送文字
  const sendMsg = React.useCallback(
    (msg) => {
      // ref.current?.focus();
      // setFocus(false)
      // setTimeout(() => {
      //   setFocus(true)
      // }, 0);
      if (send && msg.length > 0) {
        newAction({
          content: msg,
          toId: props.room?.roomId || "HN0001",
        })
          .then((act) => {
            // console.log(act)
            send(act).then(() => {
              setValue("");
              // setFocus(true)
            });
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },
    [props.room, send]
  );
  // 切换语音/键盘
  const handleVoice = React.useCallback(() => {
    setVoice(!voice);
  }, [voice]);

  const recorderManagerFunc = React.useCallback(() => {
    recorderManager.onError((res) => {});
    recorderManager.onStop((res) => {
      if (voiceLock) {
        if (res.duration < 1000) {
          Taro.showToast({
            title: "录音时间太短，请长按录音",
            icon: "none",
            duration: 2000,
          });
        } else {
          // const { tempFilePath, duration } = res;
          // Taro.showLoading({
          //   title: '语音发送中'
          // })
          // that.duration = duration
          uploadAudio(res);
        }
      } else {
        // filePath = ''
        // duration = 0
      }
    });
  }, [recorderManager, uploadAudio, voiceLock]);

  // 录音开始
  const handleRecordStart = React.useCallback(
    (e) => {
      let event = e;
      timerRef.current = setTimeout(() => {
        const options: any = {
          duration: 60000,
          sampleRate: 44100,
          numberOfChannels: 1,
          encodeBitRate: 192000,
          format: "mp3",
          frameSize: 50,
        };
        setVoiceLock(true); // 长按时设置为可发送状态
        touch.current.startPoint = event.touches[0]; // 记录触摸点初始坐标信息
        recorderManager.start(options);
        Taro.vibrateLong();
        Taro.showToast({
          title: "正在录音,往上滑动取消录音",
          icon: "none",
          duration: 60000,
        });
      }, 500);
    },
    [recorderManager]
  );

  // 录音结束
  const handleRecordEnd = React.useCallback(
    (e) => {
      // 延时处理 防止录音未调起就触发停止
      setTimeout(() => {
        recorderManager.stop();
      }, 400);
      timerRef.current && clearTimeout(timerRef.current);
      Taro.hideToast();
    },
    [recorderManager]
  );

  // 手势移动
  const handleTouchMove = React.useCallback(
    (e) => {
      if (
        Math.abs(
          e.touches[e.touches.length - 1].clientY -
            touch.current.startPoint.clientY
        ) > 100
      ) {
        // console.log("设置为不发送语音");
        Taro.showToast({
          title: "松开手指,取消录音",
          icon: "none",
          duration: 60000,
        });
        setVoiceLock(false);
      } else {
        if (!voiceLock) {
          Taro.showToast({
            title: "正在录音,往上滑动取消录音",
            icon: "none",
            duration: 60000,
          });
        }
        setVoiceLock(true);
      }
    },
    [voiceLock]
  );

  React.useEffect(() => {
    // if (Taro.getEnv() === Taro.ENV_TYPE.WEAPP) {
    //   getTemplateId().then(res => {
    //     setTemplateId(res.data.id)
    //   })
    // }
    setIphonex(() => {
      const model = Taro.getSystemInfoSync().model;
      return (
        /iphone\sx/i.test(model) ||
        (/iphone/i.test(model) && /unknown/.test(model)) ||
        /iphone\s11/i.test(model) ||
        /iphone\s12/.test(model)
      );
    });
    recorderManagerFunc();
  }, [recorderManagerFunc]);

  // 对应 onShow
  Taro.useDidShow(() => {
    // console.log('对应 登陆状态++', getLoggedIn())
    setLoginStatus(getLoggedIn());
  });

  React.useEffect(() => {
    // console.log('登陆状态', loginStatus)
    // console.log('登陆状态++', props.roomConfig)
    if (!loginStatus) {
      setRoomStatus("login");
      return;
    }
    if (props.roomConfig.chatMode == 2 || props.roomConfig.inBlackList == 1) {
      setRoomStatus("muzzle");
      return;
    }
    setRoomStatus("normal");
  }, [loginStatus, props.roomConfig]);

  // 登录
  const toLogin = React.useCallback((e) => {
    Taro.navigateTo({
      url: "/pages/login/index",
    });
  }, []);

  return (
    <View id="inputWrap">
      {roomStatus == "login" && (
        <View
          className={`${styles.inputLogin} ${isIphonex ? styles.iphonex : ""}`}
          onClick={toLogin}
        >
          仅限登录用户互动&gt;&gt;去登录
        </View>
      )}
      {roomStatus == "normal" && (
        <View
          className={`${styles.inputArea} ${isIphonex ? styles.iphonex : ""}`}
        >
          <View className={styles.voice} onClick={handleVoice}>
            {/* {voice ? "语音" : "键盘"} */}
            {voice && <Image
              src={require("@/images/keyboard.png")}
              mode="widthFix"
              lazyLoad
              className={styles.btnIcon}
            />}
            {!voice && <Image
              src={require("@/images/voice.png")}
              mode="widthFix"
              lazyLoad
              className={styles.btnIcon}
            />}
          </View>
          {voice && (
            <View className={styles.voiceBtnWrap}>
              <View
                className={styles.voiceBtn}
                onClick={handleVoice}
                onTouchStart={handleRecordStart}
                onTouchEnd={handleRecordEnd}
                onTouchMove={handleTouchMove}
              >
                按住说话
              </View>
            </View>
          )}
          {!voice && (
            <View className={styles.input}>
              <Input
                cursorSpacing={20}
                // ref={ref}
                // onClick={subscribeMessage}
                value={value}
                // focus={focus}
                adjustPosition={false}
                confirmType="send"
                onInput={(e) => setValue(e.detail.value)}
                // onFocus={(e) => {props.keyboardHeightChange(e.detail.height)}}
                // onBlur={props.keyboardHeightChange(0)}
                confirmHold
                onConfirm={(e) => sendMsg(e.detail.value)}
              />
            </View>
          )}
          {!value && (
            <View className={styles.action}>
              <Image
                src={PictureImg}
                className={styles.icon}
                mode="widthFix"
                onClick={selectImg}
              />
            </View>
          )}
          {value && (
            <View className={styles.action}>
              <Button
                className={styles.button}
                size="mini"
                type="warn"
                onClick={() => sendMsg(value)}
              >
                发送
              </Button>
            </View>
          )}
        </View>
      )}
      {roomStatus == "muzzle" && (
        <View
          className={`${styles.inputLogin} ${isIphonex ? styles.iphonex : ""}`}
        >
          健康绿色上网,共建和谐网络
        </View>
      )}
    </View>
  );
};

export default React.memo(Index);
