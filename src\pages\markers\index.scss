page{
  background: #F3F3F3;
}
.markers-page{
  padding: 30px;
  .marker-item{
    padding: 30px;
    display: flex;
    align-items: flex-start;
    background: #ffffff;
    border-radius: 20px;
    margin-bottom: 20px;
  }
  .left-image{
    width: 120px;
    height: 120px;
    margin-right: 20px;
  }
  .right-content{
    flex: 1;
  }
  .content-top{
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    .title{
      font-size: 30px;
      color: #333333;
    }
    .desc{
      flex: 1;
      font-size: 24px;
      color: #999999;
      padding: 0 20px;
      box-sizing: border-box;
      .name{
        color: #333333;
      }
    }
    .time{
      font-size: 24px;
      color: #999999;
    }
  }
  .content-bottom{
    margin-top: 20px;
    .address-detail{
      font-size: 28px;
      color: #333333;
      line-height: 40px;
    }
    .directions{
      font-size: 28px;
      color: #999999;
      margin-top: 20px;
    }
    .remark{
      font-size: 26px;
      color: #999999;
      margin-top: 20px;
    }
  }
  .popup-voice-msg{
    .voice-msg-item{
      // transform: rotate(0);
      flex-direction: row-reverse;
      padding: 0;
      justify-content: right;
    }
    .voice-msg-item-audio{
      text{
        transform: rotate(180deg);
      }
    }
  }
}
