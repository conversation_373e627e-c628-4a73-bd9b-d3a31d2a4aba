import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { View, Image, Text, ScrollView, Video } from '@tarojs/components'

import PlayView from './components/PlayView';

import './index.scss'

import { getPlaybackInfo, getLiveInfo } from '../../api/modules/live'
import { showToast } from '@/tools';


interface Props {
}

interface Audio {
  src: string,
  title: string,
  coverImgUrl:string
}

interface Live {
  endTime: string
  name: string
  playUrl: string
  programId: string
  psid: string
  startTime: string
  isLive?: boolean
}
interface State {
  radioUrl: string,
  videoUrl: string,
  liveList: Live[],
  audioInfo: Audio,
  isLoad: number, // 0 : 初始化， 1： 加载中，2：开始播放
  audioIndex: number
}

export default class Index extends Component<Props, State> {
  private viewRef;
  private playView;


  // config: Config = {
  //   navigationBarTitleText: '直播',
  //   navigationStyle: 'default',
  // }


  constructor(props: Props) {
    super(props);

    this.state = {
      radioUrl: '',
      videoUrl: '',
      liveList: [],
      audioInfo: {
        src: '',
        title: '',
        coverImgUrl: ''
      },
      isLoad: 0,
      audioIndex: -1
    }
  }
  componentWillMount () {
    let date = new Date();
    let hours = date.getHours();

    // 获取数据
    Promise.all([getLiveInfo(), getPlaybackInfo()]).then(([liveInfo, playbackInfo]) => {
      this.setState({
        radioUrl: liveInfo.radioUrl,
        videoUrl: liveInfo.videoUrl,
        liveList: playbackInfo.filter((item:Live, index:number) => {
          let start = parseInt(item.startTime);
          let end = parseInt(item.endTime);

          playbackInfo[index]['isLive'] = hours < end;
          return hours >= start;
        }),
      });
    })
   }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidShow () {
  }

  componentDidHide () { }

  onShareAppMessage(res) {
    return {
      title: '交广直播',
      path: '/pages/live/index'
    }
  }

  playAudio(index) {
    let { liveList } = this.state;
    let live:Live = liveList[index];
    let playUrl:string = '';
    if (!live.isLive) { // 回听

      // 判断url是否存在
      if (live.playUrl.length === 0) {
        showToast('该节目暂不支持回听！');
        return;
      }
      playUrl = live.playUrl;
    } else { // 直播
      playUrl = this.state.radioUrl;
    }

    // if (this.audio) {
    //   this.audio.seek(0);
    // }

    // this.setState({
      // audioInfo: {
      //   src: playUrl,
      //   title: live.name,
      //   coverImgUrl: 'https://dev.jgrm.net/resource/image/b0fa7c4b8c8b41118e9c3f4e4a7a0ef9',
      // }
    // });
    this.playView.playAudio({
      src: playUrl,
      title: live.name,
      coverImgUrl: 'https://dev.jgrm.net/resource/image/b0fa7c4b8c8b41118e9c3f4e4a7a0ef9',
    });

    this.setState({
      audioIndex: index
    });

  }

  updateState() {
    console.log('updateState, 333');
    this.setState({
      audioIndex: -1
    });
  }

  render () {
    let { videoUrl, liveList, isLoad, audioInfo } = this.state;

    console.log(isLoad, '666');
    return (
      <View
        ref={(item) => {
          this.viewRef = item;
        }}
      >


      <PlayView
        ref={(item) => {
          this.playView = item;
        }}
        parentUpdate={this.updateState.bind(this)}
        videoUrl={videoUrl}
      />

        <View className="title">
          <Text className="txt">
            回听列表
          </Text>
        </View>
        <ScrollView
          className="list-wrap"
          scrollY
        >
          <View className="list">
            {
              liveList.length > 0 &&
              liveList.map((item, index) => {
                return <View className={['item ', this.state.audioIndex == index ? 'this' : ''].join()} key={item.psid}>
                        <View className="play" onClick={this.playAudio.bind(this, index)}>
                          <Text className="item-title">{item.name}</Text>
                          <View className="item-time">
                            <Text className="mask">{!item.isLive ? '回听' : '直播'}</Text>
                            <Text className="time">{item.startTime} - {item.endTime}</Text>
                          </View>
                        </View>
                      </View>
              })
            }
{/*
          <View className='item'>
            <View className='play'>
              <Text className='item-title'>南方交通-直播</Text>
              <View className='item-time'>
                <Text className='mask'>回听</Text>
                <Text className='time'>00:00 - 01:00</Text>
              </View>
            </View>
          </View> */}
          </View>
        </ScrollView>
      </View>
    )
  }
}
