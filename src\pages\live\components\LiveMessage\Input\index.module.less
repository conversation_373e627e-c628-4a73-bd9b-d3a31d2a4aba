// .inputArea.iphonex{
//   padding-bottom: 50px;
// }
.inputArea {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 5px;
  box-sizing: border-box;
  background-color: #F5F6F7;
  border-top: 1px solid #eaeaea;
  .input {
    // width: 70%;
    flex: 1;
    padding: 10px;
    font-size: 36px;
    input {
      background-color: #FFF;
      padding: 10px;
      border-radius: 10px;
      box-sizing: border-box;
      height: 64px;
      line-height: 64px;
      display: block;
    }
  }
  .action {
    flex-grow: 0;
    flex-shrink: 1;
    padding: 0 20px 0 10px;
    display: flex;
    justify-content: space-around;
    align-items: center;

    > .icon {
      width: 60px;
      height: auto;
    }
    > .button{
      background: #FD4925;
    }
  }
  .voice{
    // margin-right: 10px;
    padding: 0 10px 0 20px;
    display: flex;
    align-items: center;
  }
  .voiceBtnWrap{
    flex: 1;
    padding: 10px 20px;
    box-sizing: border-box;
    border-radius: 10px;
    .voiceBtn{
      display: block;
      width: 100%;
      height: 100%;
      padding: 10px;
      border-radius: 10px;
      background-color: #FFF;
      box-sizing: border-box;
      font-size: 36px;
      text-align: center;
    }
  }
  .btnIcon{
    width: 60px;
    height: auto;
  }
}
.inputLogin{
  height: 80px;
  background: #fd4925;
  color: #ffffff;
  text-align: center;
  font-size: 28px;
  line-height: 80px;
}
// .inputLogin.iphonex{
//   padding-bottom: 50px;
// }
