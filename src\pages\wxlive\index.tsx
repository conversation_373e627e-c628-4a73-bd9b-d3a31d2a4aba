import Taro, { getCurrentInstance } from '@tarojs/taro'
import React, { Component }  from 'react'
import {  View, Image, } from '@tarojs/components'
import { showToast } from '@/tools'
import './index.scss'
import { getRoomList } from '../../api/modules/live'


interface State {
  list: any[],
  params: any,
}
export default class Index extends Component<any, State> {

  // config: Config = {
  //   navigationBarTitleText: '直播间列表'
  // }
  $instance: any = getCurrentInstance()

  constructor(props) {
    // super(props);
    super(props);
    this.state = {
      list: [],
      params: {}
    };
  }
  componentWillMount () {
    const params = this.$instance.router.params;
    // const oid = params.oid;
    // const type = params.type;
    this.setState({
      params: params
    })
    getRoomList({
      pageNo: 1,
      pageSize: 100
    }).then((res) => {
      let newrooms = [];
      // 直播间状态。101：直播中，102：未开始，103已结束，104禁播，105：暂停，106：异常，107：已过期
      // 禁播  已过期 已结束且没有回放 不显示
      newrooms = res.list.filter(item => (item.liveStatus === 103 && item.closeReplay === 0) || (item.liveStatus !== 103 && item.liveStatus !== 104 && item.liveStatus !== 107));
      this.setState({
        list: newrooms
      })
    }).catch(err => {
      showToast(err);
      console.error(err);
      return Promise.reject(err);
    })
  }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidHide () {}

  onError(e) {
    console.error(e);
  }

  onLoad(e) {
    console.log(e);
  }
  toLive(id){
    let roomId = id // 填写具体的房间号，可通过下面【获取直播房间列表】 API 获取
    let customParams = encodeURIComponent(JSON.stringify({ path: 'pages/index/index', pid: 1 })) // 开发者在直播间页面路径上携带自定义参数（如示例中的path和pid参数），后续可以在分享卡片链接和跳转至商详页时获取，详见【获取自定义参数】、【直播间到商详页面携带参数】章节（上限600个字符，超过部分会被截断）
    Taro.navigateTo({
        url: `plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${roomId}&custom_params=${customParams}`
    })
    // 其中wx2b03c6e691cd7370是直播组件appid不能修改
  }
  render () {
    // console.log(this.state.toUrl, '55');
    let { list } = this.state;
    return (
      <View className="flex-wrap">
        {list.map((item) => {
          return <View className="flex-item" key={item.roomId} onClick={this.toLive.bind(this, item.roomId)}>
                    <View className="room-img">
                      <Image src={item.feedsImg} />
                    </View>
                    <View className="room-name">
                      {item.name}
                    </View>
                    <View className={`live-status ${item.liveStatus === 103 ? "replay" : null}`}>
                      {
                        {
                          // 101：直播中，102：未开始，103已结束，104禁播，105：暂停，106：异常，107：已过期
                          101: '直播中',
                          102: '未开始',
                          103: '直播回放',
                          104: '禁播',
                          105: '暂停',
                          106: '异常',
                          107: '已过期',
                        }[item.liveStatus]
                      }
                    </View>
                  </View>
        })}
      </View>
    )
  }
}
