/*
 * @Description: 简易版的节流函数
 */

/**
 * @param fn : 回调函数
 * @param threshold : 时间,单位毫秒
 */
 export function throttle(fn: Function, threshold: number = 1500) {
  if (threshold === null) {
    threshold = 1500
  }
  let _lastExecTime: null | number = null;
  let context = this
  return function (...args: any[]): void {
    let _nowTime: number = new Date().getTime();
    if (_nowTime - Number(_lastExecTime) > threshold || !_lastExecTime) {
      fn.apply(context, args);
      _lastExecTime = _nowTime
    }
  }
}

export const debounce = <T extends any[], R>(
  fn: (...args: T) => R,
  timeout: number,
) => {
  let handle = 0
  let lastArgs: T | null = null
  const ret = (...args: T) => {
    lastArgs = args
    clearTimeout(handle)
    handle = setTimeout(() => {
      lastArgs = null
      fn(...args)
    }, timeout) as unknown as number
  }
  ret.flush = (): R | void => {
    clearTimeout(handle)
    if (lastArgs) {
      const _lastArgs = lastArgs
      lastArgs = null
      return fn(..._lastArgs)
    }
  }
  // ret.cancel = () => {
  //   lastArgs = null
  //   clearTimeout(handle)
  // }
  return ret
}
