import { setWebViewCallbackURL } from '@/gdata';
import { showModal, showToast } from '@/tools';
import { View } from '@tarojs/components';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import React, { Component }  from 'react'
import { OrderCategory } from '@/enums';
import { filterSubscribeMessage } from "@/util";
import { getOrderInfo, getXcxMessages, getXcxMessagesV2 } from "@/api/modules/user";
import { getOrderPayStatus } from "@/api/modules/order";
import './index.scss';


type PaymentOptions = Taro.tradePay.Option & { orderInfo?: any };

// 当前版本taro中不包含 requestOrderPayment ，此处包装为 Promise 形式
function requestOrderPayment(options) {
  return new Promise((resolve, reject) => {
    const paymentOptions = Object.assign({}, options, {})
    paymentOptions.success = function(res) {
      resolve(res);
    }
    paymentOptions.fail = function(err) {
      reject(err);
    }
    Taro.tradePay(paymentOptions)
  })
}

export default class Index extends Component {
  $instance: any = getCurrentInstance()
  isWeapp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP
  isAlipay = Taro.getEnv() === Taro.ENV_TYPE.ALIPAY
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentWillMount () {
    let successURL = decodeURIComponent(this.$instance.router.params.success);
    let failURL = decodeURIComponent(this.$instance.router.params.fail);
    let url = decodeURIComponent(this.$instance.router.params.url);
    if (this.isAlipay) {
      successURL = this.$instance.router.params.success;
      failURL = this.$instance.router.params.fail;
    }
    let category = decodeURIComponent(this.$instance.router.params.category); // 订单类型
    // console.log('componentWillMount: ', this.$instance.router.params)
    // console.log('window.wx: ', wx);
    const options = Taro.getLaunchOptionsSync();
    getOrderInfo(this.$instance.router.params.orderId).then(res => {
      // Taro.requestOrderPayment();
      const paymentOptions: PaymentOptions = {
        /** 接入 小程序支付 时传入此参数。此参数为支付宝交易号，注意参数有大小写区分（调用 小程序支付 时必填） */
        tradeNO: res,
        // /** 接入 预授权支付 时传入此参数 完整的支付参数拼接成的字符串，从服务端获取（调用 支付宝预授权 时必填） */
        // orderStr: res.paySign
      }
      if (res.orderInfo) {
        paymentOptions.orderInfo = res.orderInfo;
        if (!('scene' in res.orderInfo)) {
          paymentOptions.orderInfo.scene = options.scene;
        }
        // paymentOptions.order_info.scene = options.scene;
      }
      return requestOrderPayment(paymentOptions).then((res: {memo:string,resultCode:string}) => {
        // console.log("🚀 ~ Index ~ returnrequestOrderPayment ~ 支付成功:", res)
        setTimeout(() => {
          // 支付宝支付API无法获取准确的支付状态，此处通过接口订单状态判断
          getOrderPayStatus(this.$instance.router.params.orderId).then(res => {
            if (res.status == 2 || res.status == -1 || res.status == -2) {
              // 设置临时的回调URL
              setWebViewCallbackURL(successURL);
              Taro.navigateBack();
              return
            } else {
              showToast('支付失败');
              setWebViewCallbackURL(failURL);
              Taro.navigateBack();
            }
          }).catch(err => {
            // console.log("🚀 ~ Index ~ returnrequestOrderPayment ~ getOrderPayStatus ~ err:", err)
          });
        }, 300);
      }).catch(err => {
        // showToast('err' + err);
        console.log(err);
        setWebViewCallbackURL(failURL);
        showToast(err.errMsg);
        Taro.navigateBack();
        // showModal('提示', err.errMsg, {
        //   success: () => {
        //     Taro.navigateBack();
        //   }
        // })
      });
    }).catch(err => {
      showModal('提示', String(err), {
        showCancel: false,
        complete: () => {
          Taro.navigateBack();
        }
      })
    });
  }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidShow () {
  }

  componentDidHide () { }

  // config: Config = {
  //   navigationBarTitleText: '收银台'
  // }

  showError(e:string):void {
    console.log(e, 333);
  }
  loadFunishPage(e:string):void {
    console.log(e, 'nnn');
  }

  render () {
    return (
      <View className="page">
        正在请求支付...
      </View>
    )
  }
}
