import { resolve } from 'path';
const path = require("path");

let TARO_ENV = process.env.TARO_ENV
let FolderName = TARO_ENV == 'weapp'? 'WEAPP': 'ALIPAY'
const config = {
  projectName: 'jglh-mp',
  compiler: {
    type: 'webpack5',
    // 仅 webpack5 支持依赖预编译配置
    prebundle: {
      enable: true
    }
  },
  date: '2022-8-4',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2
  },
  sourceRoot: 'src',
  outputRoot: TARO_ENV == 'weapp'? `dist/${FolderName}`: `dist/${FolderName}`,
  plugins: [
    [
      "@tarojs/plugin-inject",
      {

        components: {
          Button: {
            bindagreeprivacyauthorization: ""
          }
        }
      }
    ]
  ],
  defineConstants: {
  },
  alias: {
    '@': path.resolve(__dirname, '..', 'src')
  },
  copy: {
    patterns: [
    ],
    options: {
    }
  },
  framework: 'react',
  // 小程序端配置
  mini: {
    // prerender: {
    //   match: 'pages/index/index' // 所有以 `pages/shop/` 开头的页面都参与 prerender
    // },
    miniCssExtractPluginOption: {
      ignoreOrder: true,
    },
    postcss: {
      autoprefixer: {
        enable: true,
        config: {
        }
      },
      pxtransform: {
        enable: true,
        config: {

        }
      },
      // 小程序端样式引用本地资源内联配置
      url: {
        enable: true,
        config: {
          limit: 1024 // 设定转换尺寸上限
        }
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    }
  },
  // H5 端配置
  h5: {
    publicPath: '/',
    staticDirectory: 'static',
    postcss: {
      autoprefixer: {
        enable: true,
        config: {
        }
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
    esnextModules: ['taro-ui']
  }
}

export default function (merge) {
  if (process.env.NODE_ENV === 'development') {
    return merge({}, config, require('./dev'))
  }
  if (process.env.NODE_ENV === 'staging') {
    return merge({}, config, require('./staging'))
  }
  return merge({}, config, require('./prod'))
}
