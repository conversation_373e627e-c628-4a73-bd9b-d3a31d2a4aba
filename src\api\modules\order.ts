import Taro from '@tarojs/taro';
import API from '../api';
import { doGet, doPost } from '../request'

const isWeapp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP
const isAlipay = Taro.getEnv() === Taro.ENV_TYPE.ALIPAY
// 获取首页信息
API.extend({
  '/order/find/by/orderid': '/Radio/order/find/by/orderid', // 订单状态：1-未支付，2-已支付，-1-已退款，-2-部分退款
});

// 获取订单支付状态
export function getOrderPayStatus(orderId) {
  return doGet(API.dget('/order/find/by/orderid'), { orderId });
}
