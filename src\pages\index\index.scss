page {
  height: 100%;
  background: #f3f3f3;
  box-sizing: border-box;
  margin-bottom: 0;
}
.pageBox {
  padding: 0 30px;
}

.index {
  background-repeat: no-repeat;
  background-size: cover;
  min-height: 100%;
}
.rotation {
  margin-top: 20px;
  position: relative;
  .bg-box {
    height: 300px;
  }
  .swiper-item {
    border-radius: 20px;
  }
  .bg-item {
    height: 300px;
    border-radius: 20px;
    .bgImage {
      width: 100%;
      height: 100%;
      border-radius: 20px;
      background-size: cover;
    }
  }

  // 指示点样式
  .spot-pagination {
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 20px;
    width: 100%;
    .spot-pagination-bullet {
      margin-right: 10px;
      width: 16px;
      height: 4px;
      background: #ffffff;
      opacity: 0.4;
      border-radius: 2px;
    }
    // 当前指示点样式
    .spot-pagination-bullet-active {
      opacity: 1;
    }
  }
}

.new-menu {
  // padding: 0 20px;
  padding-top: 27px;
  padding-bottom: 19px;
  // background: #fff;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  // background: #ffffff;
  // padding-top: 35px;
  .menu-item {
    min-width: 20%;
    &:nth-child(n + 5) {
      max-width: 20%;
    }
    text-align: center;
    // width: 20%;
    // float: left;
    flex: 1;
    // margin-top: 7px;
    padding-bottom: 26px;
    // padding: 5px 0;

    transition: all 250ms;
    &.menu-item-hover {
      background: rgb(228, 228, 228);
    }
    .menu-item-img {
      position: relative;
      width: 83px;
      height: 83px;
      margin: 0 auto;
      .img {
        width: 100%;
        height: 100%;
      }

      .brdige {
        position: absolute;
        right: -30px;
        width: 60px;
        height: 30px;
      }
    }
    .menu-item-text {
      font-size: 24px;
      // color: #fff;
    }
  }
}

.ad-loop {
  // padding-top: 36px;
  padding-bottom: 5px;
  margin-bottom: 18px;
  height: 125px;
  width: 100%;

  .img {
    width: 100%;
    height: 100%;
  }

  .ad-swuper {
    width: 100%;
    height: 100%;
  }
}

.news {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 26px;
  padding-bottom: 10px;
  background: #fff;
  .news-img {
    line-height: 1;
    margin-right: 20px;
    .img {
      // width: 135px;
      // height: 40px;
      width: 110px;
      height: 30px;
      vertical-align: top;
      vertical-align: middle;
    }
  }
  .news-list {
    flex: 1;
  }

  .news-swuper {
    position: relative;
    height: 70px;
    line-height: 1;
    box-sizing: border-box;
    padding: 10px 0;
    .news-item {
      display: flex;
      align-items: center;
      vertical-align: middle;
      line-height: 1;
    }
    .news-text {
      word-break: break-all;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      font-size: 24px;
      display: block;
    }
  }
}

.new-recommend {
  background: #fff;
  display: flex;
  padding: 0 14px;
  padding-top: 10px;
  .block {
    height: 154px;
    width: 50%;
    padding: 0 12px;
    .img {
      border-radius: 12px;
      width: 100%;
      height: 100%;
    }
  }
}

.bus {
  background: #fff;
  // display: flex;
  // justify-content: space-between;
  // flex-wrap: wrap;
  // padding: 30px 30px 0 30px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 150px 150px;
  gap: 20px;
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
  border-radius: 20px;
  // overflow: hidden;
  .block {
    .img {
      // border-radius: 12px;
      border-radius: 20px;
      width: 100%;
      height: 100%;
    }
  }
}
.btn-live {
  bottom: 100px;
}
.live {
  width: 100%;
  padding-bottom: 20px;
  // padding: 0 25px;
  box-sizing: border-box;
  margin: 0 auto;
  background: #f2f4f5;
  .container {
    width: 100%;
    background: url(./images/bg.png) no-repeat center top;
    background-size: 100% 100%;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    border-radius: 20px;
  }
  .text {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 57px 0 0 54px;
    .online {
      width: 204px;
      height: 38px;
      margin-bottom: 20px;
      background: url(./images/text.png) no-repeat center;
      background-size: 100% 100%;
    }
    .detail {
      font-size: 24px;
      font-weight: bold;
      color: #fd222b;
      padding-right: 22px;
      background: url(./images/arrow.png) no-repeat right center;
      background-size: 18px 18px;
      line-height: 1;
      margin-left: 36px;
    }
  }
  .room-swiper {
    width: 240px;
    height: 200px;
    padding: 20px 0;
    box-sizing: border-box;
    margin-right: 60px;
    overflow: hidden;
    .item {
      // width: 200px;
      // height: 160px;
      overflow: hidden;
      z-index: 5;
      opacity: 0.5;
      width: 120px !important;
      height: 120px !important;
      top: 20px;
      transition: height linear 0.2s, top linear 0.2s;
      image {
        width: 100%;
        height: 100%;
        border-radius: 10px;
      }
      &.active {
        z-index: 10;
        opacity: 1;
        top: 0px;
        width: 160px !important;
        height: 160px !important;
      }
    }
  }
}
.jglh-search {
  background: transparent;
  padding: 0;
  padding-top: 20px;
  .van-search__content {
    background: #ffffff;
    border-radius: 55px;
    overflow: hidden;
  }
}
