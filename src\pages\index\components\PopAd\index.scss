
.pop-ad-wrap {
  width: 100%;
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: rgba(0,0,0,.5);
  .pop-ad{
    width: 520px;
    box-sizing: border-box;
    text-align: center;
    position: relative;
    image{
      display: block;
      width: 100%;
      border-radius: 18px;
    }
    .close-btn{
      margin-top: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .pop-count-down{
    position: absolute;
    right: 0;
    top: 0;
    background: rgba(0,0,0,.5);
    // background: rgba(218, 0, 0);
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 30px;
    color: #ffffff;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    // transform: scale(.5);
    // transform-origin: right top;
    &.pop-count-down-hide{
      visibility: hidden;
    }
  }
}
