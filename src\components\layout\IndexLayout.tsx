import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { View } from '@tarojs/components'
import { AtNavBar } from 'taro-ui'
// import UserWeather from '../userWeather'
import { getImageURL , login } from '@/tools';
import { getUserInfo, getLoggedIn } from '@/gdata';


import './IndexLayout.scss'

interface Props {
  children?: any;
  pageBg: string
}

interface State {
  statusBarHeight: number,
  height: number,
  avage: string,
  systemInfo: any
}

export default class IndexLayout extends Component<Props, State> {


  // config: PageConfig = {
  //   navigationBarTitleText: '首页',
  // }

  constructor(props) {
    super(props);

    this.state = {
      statusBarHeight: 0,
      height: 0,
      avage: '',
      systemInfo: {}
    };
  }

  // 计算navbarheight
  computedNavBarHeight() {
    if (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'alipay') {
      let rect = Taro.getMenuButtonBoundingClientRect ? Taro.getMenuButtonBoundingClientRect() : { top: 0, height: 0 };
      let res:any = Taro.getSystemInfoSync();
      // console.log(res, rect);
      // let pH = res.screenHeight - res.windowHeight - 20;
      let navBarHeight = (function () {
        let gap = rect.top - res.statusBarHeight;
        return res.statusBarHeight + 2 * gap + rect.height;
      })();
      let ios = !!(res.system.toLowerCase().search('ios') + 1);
      let navBarExtendHeight = 0;
      if (ios) {
        navBarExtendHeight = 4; // 下方扩展4像素高度 防止下方边距太小
      }

      this.setState({
        statusBarHeight: res.statusBarHeight || 0,
        height: navBarHeight + navBarExtendHeight,
        systemInfo: res,
      });
    }
  }


  componentDidMount () {
    this.computedNavBarHeight();
  }

  componentWillUnmount () { }

  componentDidShow () {
    const avage = getUserInfo().portrait || 'Fr9WEh0tOeRea8vZjSgXgr2_K0By';
    this.setState({
      avage: getImageURL(avage)
    });
  }

  componentDidHide () { }

  showTip(str) {
    Taro.showToast({
      title: str,
      icon: 'none'
    });
  }

  render () {
    const { statusBarHeight, height, avage, systemInfo } = this.state;
    let { children, pageBg } = this.props;
    pageBg = pageBg ? `https://img.jgrm.net/${pageBg}?v=400` : 'https://img.jgrm.net/Fgif0ceZP-_60role0f0PJYNn7ez?v=400';
    let navigationStyle = `padding-top: ${statusBarHeight}px;height: ${height}px;`;
    let nabBarTitleStyle = `height: ${height - statusBarHeight}px; display: inline-flex;align-items: center;`;
    let nabBarBgStyle = `background-color: transparent !important;top: ${statusBarHeight}px;`
    let pageBgStyle = `background-color: #F3F3F3;padding-top: ${height}px;`;
    // const pageBgStyle = `background-image: url(${pageBg});padding-top: ${height}px;`;
    if (systemInfo.platform == 'windows') { // Windows 微信客户端不支持 自定义导航栏
      navigationStyle += 'display: none;'
      pageBgStyle += 'padding-top: 0px;'
    }
    return (
      <View className="index" style={pageBgStyle}>
        <View className="navbar" style={navigationStyle}>
          <AtNavBar
            className="navbar2"
            border={false}
            fixed
            customStyle={nabBarBgStyle}
          >
            {/* <UserWeather
              avage={avage}
            /> */}
            <View className="title" style={nabBarTitleStyle}>交广领航</View>
          </AtNavBar>
        </View>
        {children}
      </View>
    )
  }
}
