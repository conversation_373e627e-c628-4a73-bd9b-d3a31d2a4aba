import Taro from '@tarojs/taro'

export const isUserSelfMsg = item => {
  let gdata = JSON.parse(Taro.getStorageSync('gdata'))
  let isSelf = false
  if(gdata.user.uid == item?.sender?.uid){
    isSelf = true
  }
  return isSelf
}
/**
 * @description: 根据消息中的randomNumber字段判断是否存在
 * @param {*} timestamp randomNumber随机数
 * @param {*} list
 * @return {*}
 */
export const isMsgExist = (timestamp, list) => {
  return list.findIndex((item) => {
    return item.randomNumber == timestamp
  })
}
/**
 * @description: 根据本地缓存中的用户信息生成聊天室用户信息字段
 * @return {*}
 */
export const generateUserField = () => {
  let gdata = JSON.parse(Taro.getStorageSync('gdata'))
  if(gdata?.user?.uid){
    return {
      name: gdata.user.name,
      phone: gdata.user.phone,
      portrait: gdata.user.portrait,
      sid: gdata.user.sid,
      t: gdata.user.t,
      type: gdata.user.type,
      uid: gdata.user.uid,
      vipStatus: gdata.user.vip,
    }
  }
  return {}
}

/**
 * @description: 组合发送聊天室消息体
 * params 中含有一下参数
 * @param {string} content 消息内容(文本消息语音/图片/小视频的资源id)
 * @param {number} type 消息类型值
 * @param {string} toId 聊天室id
 * @param {number} mtype 聊天场景（1：私信2：群聊）
 * @param {number} speech 音频时长，非音频消息类型该值默认为-1、0 , 单位毫秒
 * @return {object}
 */
 export async function newAction(params:any) : Promise<any> {
  let action:any = {
    type: 0,
    toId: 'HN0001',
    mtype: 2,
    speech: -1,
    ...params
  }
  const time = new Date().getTime()
  // const res = await getReqId()
  return {
    ...action,
    randomNumber: time // 随机数，用于判断消息是否发送成功
  }
  // switch (type) {
  //   case 0: // 文本
  //     return {
  //       content,
  //       type,
  //       mtype,
  //       toId,
  //       speech,
  //       randomNumber: time // 随机数，用于判断消息是否发送成功
  //     }
  //     break;
  //     case 1: // 语音
  //       return {
  //         content,
  //         type,
  //         mtype,
  //         toId,
  //         speech,
  //         randomNumber: time // 随机数，用于判断消息是否发送成功
  //       }
  //       break;
  //     case 2: // 图片
  //       return {
  //         content,
  //         type,
  //         mtype,
  //         toId,
  //         speech,
  //         randomNumber: time // 随机数，用于判断消息是否发送成功
  //       }
  //       break;

  //   default:
  //     break;
  // }
}
