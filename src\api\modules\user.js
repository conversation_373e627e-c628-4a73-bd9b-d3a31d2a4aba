import Taro from '@tarojs/taro';
import API from '../api';
import { doGet, doPost } from '../request'

const isWeapp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP
const isAlipay = Taro.getEnv() === Taro.ENV_TYPE.ALIPAY
// 获取直播信息
API.extend({
  "/user/info": "/Auth/v3/user/info", // 用户信息
  "/user/vip/infos": "/Radio/vip/ads/infos", // 获取是否vip用户
  "/user/order/list": "/Radio/mycenter/order/menu/list", // 获取订单模块内容
  "/user/mycenter/list": "/Radio/mycenter/menu/list", // 获取我的服务

  "/user/gold/num": "/app/api/wealth/get/account", // 获取用户金币数量
  "/user/order/info": "/Radio/pay/charge", // 获取订单信息
  "/user/sign": "/app/api/wealth/save/sign/uid", // 签到
  "/user/sing/detial": "/app/api/wealth/list/sign/rule/detail", // 签到详情
  "/xcx/template/messages": "/Radio/app/api/subscribe/xcx/template/list", // 小程序订阅消息
  "/xcx/template/messages/v2": "/Radio/app/api/subscribe/xcx/template/list/v2", // 小程序订阅消息V2
  // 会员类型
  '/vip/ads/infos': '/Radio/vip/ads/infos',
});

// 签到详情
export function signInfo() {
  return doGet(API.dget('/user/sing/detial'));
}

// 签到
export function doSign(type = 2) {
  return doPost(API.dget('/user/sign'), { doubleAward: type });
}

// 获取订单信息
export function getOrderInfo(oid) {
  let _channel = 'wx_xcx';
  if (isAlipay) {
    _channel = 'alipay_xcx';
  }
  return doPost(API.dget('/user/order/info'), { orderId: oid, channel: _channel });
}

// 获取我的服务
export function getMycenterList() {
  const platform = isAlipay ? 'ALIPAY_XCX' : 'XCX';

  const params = {
    platform,
  };
  return doGet(API.dget("/user/mycenter/list"), params);
}

// 获取用户金币数量
export function getUserGold() {
  return doGet(API.dget('/user/gold/num'));
}

export function getUserInfo() {
  return doGet(API.dget('/user/info'));
}

export function getUserVipInfos() {
  return doGet(API.dget("/user/vip/infos"));
}

export function getUserOrderList() {
  const platform = isAlipay ? 'ALIPAY_XCX' : 'XCX';

  const params = {
    platform,
  };
  return doGet(API.dget("/user/order/list"), params);
}
// 小程序订阅消息
export function getXcxMessages() {
  return doGet(API.dget("/xcx/template/messages"));
}

// 小程序订阅消息V2
export function getXcxMessagesV2() {
  return doGet(API.dget("/xcx/template/messages/v2"));
}
export const ret = {};

/**
 * 获取会员类型
 */
export function getVipAdsInfo () {
  return doGet(API.dget('/vip/ads/infos'))
}
