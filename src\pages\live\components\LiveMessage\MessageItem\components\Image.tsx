import React from 'react'
import { View,Image } from "@tarojs/components";
import Taro from '@tarojs/taro'
import { getImageURL } from "@/util";
import styles from '../index.module.less'

const Index: React.FC<{
  content: string
}> = props => {

  const preview = React.useCallback((url: string) => {
      Taro.previewImage({
        urls: [url]
      }).then()
  }, [])

  return  <View className={`${styles.content} ${styles.image}`}>
      <Image lazyLoad
        className={styles.image}
        showMenuByLongpress
        src={getImageURL(props.content, '?imageslim')}
        mode="widthFix"
        onClick={() => preview(getImageURL(props.content))}
      />
    </View>
}
export default Index
