// import { host } from './index';

// 团购首页
export const MALL_HOME = `${BASE_URL}/actions/app/mcar/?title=${encodeURIComponent(
  "领航商城"
)}#/mall?home&platform=weapp`;

// export const MALL_HOME = `http://************/jglh-webapp/dist/?title=${encodeURIComponent('领航商城')}#/mall?home&platform=weapp`;

// 客服与帮助
export const HELP = `${BASE_URL}/actions/app/help`;

// 意见反馈
export const FEEDBACK = `${BASE_URL}/actions/app/feedback/`;

// 高速路况
export const HIGHWAY_TRAFFIC = `${BASE_URL}/actions/app/tracffic`;
// export const HIGHWAY_TRAFFIC = `https://dev.jgrm.net/actions/app/auth/?auth_from=aHR0cHMlM0ElMkYlMkZkZXYuamdybS5uZXQlMkZhY3Rpb25zJTJGZGVtbyUyRm9uZS1wYWdlLmh0bWw%3D&t=1576031983786&auth=jglh`;

// 黄页
export const YellowPage = `${BASE_URL}/actions/app/phoneBook`;

// 用户协议
export const USER_AGREEMENT = `${BASE_URL}/actions/app/agreement`;

// 洗车商家列表
export const CAR_WASH_SHOP_LIST = `${BASE_URL}/actions/app/mcar?title=${encodeURIComponent(
  "洗车美容"
)}#/shops/wash`;

// 订单列表
export const CAR_WASH_ORDER_LIST = `${BASE_URL}/actions/app/mcar?title=${encodeURIComponent(
  "我的订单"
)}#/orders`;

// 签到 https://dev.jgrm.net/actions/app/mcar/?env=weixin#/virtual/get/gold
export const USER_SIGN = `${BASE_URL}/actions/app/mcar?env=weixin&title=${encodeURIComponent(
  "签到"
)}#/virtual/get/gold`;

// webapp base url
export const WEBAPP_BASE = `${BASE_URL}/actions/app/mcar/`;

// 交广领航搜索页面
export const JGLH_SEARCH = `${BASE_URL}/actions/app/mcar?title=${encodeURIComponent(
  "搜索"
)}#/jglh/search`;

// 对外业务，页面模块名
export const BizPages = {
  CAR_WASH: "carwash",
  CAR_WASH_ORDERS: "carwash-orders",
};
