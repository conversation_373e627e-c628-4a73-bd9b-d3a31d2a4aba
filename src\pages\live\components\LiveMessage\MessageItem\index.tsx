import React from "react";
import Taro from "@tarojs/taro";
import { View, Image } from "@tarojs/components";
import { Loading, Icon } from "@antmjs/vantui";
import { getImageURL } from "@/util";
import { isUserSelfMsg, newAction } from "@/util/live";

import Text from "./components/Text";
import ImageMsg from "./components/Image";
// import Navigator from './components/Navigator'
import KeyWordReply from "./components/KeyWordReply";
import Avatar from "./components/UserAvatar/Index";
import Audio from "./components/Audio";
import context from "../context";

import styles from "./index.module.less";

const Index: React.FC<{
  // id: any,
  message: APP.Message;
  onPlayAudio: any;
  playStatus: boolean;
  sessionId: any;
}> = (props) => {
  const sendingTimer = React.useRef<any>();
  const [sending, setSending] = React.useState(
    props.message.sending || "complete"
  );

  // 获取mesages.tsx提供的上下文
  const send = React.useContext(context);
  const handleReSendMsg = React.useCallback(
    (e) => {
      if (send) {
        newAction({
          content: props.message.content,
          type: props.message.type,
          toId: props.message.toId,
          mtype: props.message.mtype,
          speech: props.message.speech,
        }).then((act) => {
          act.randomNumber = props.message.randomNumber;
          send(act).then().catch();
        });
      }
    },
    [props.message, send]
  );

  React.useEffect(() => {
    // setSending(props.message.sending)
    // console.log('sending状态++++', props.message.sending)
    if (props.message.sending == "start") {
      sendingTimer.current = setTimeout(() => {
        setSending("error");
      }, 10000);
    }
    if (props.message.sending == "complete") {
      setSending("complete");
      clearTimeout(sendingTimer.current);
    } else if (props.message.sending == "start") {
      setSending("start");
    }
    return () => {
      clearTimeout(sendingTimer.current);
    };
  }, [props.message.sending]);
  // const playAudio = React.useContext(context);
  // console.log(props.message)
  let isSelf = isUserSelfMsg(props.message);
  let item: JSX.Element = <></>;
  const LoadingFlag = <Loading size="16px" />;
  const sendError = (
    <Icon
      className={styles.msgStatusIcon}
      name="warning"
      size="16px"
      color="#fd4925"
      onClick={handleReSendMsg}
    />
  );
  let CustAvatar;
  let name;

  if (props.message.type != 5) {
    CustAvatar =
      !isSelf &&
      props.message.sender.portrait &&
      !props.message.sender.portrait.startsWith("/storage/") ? (
        <Image
          src={getImageURL(props.message.sender.portrait)}
          className={styles.messageAvatar}
        />
      ) : (
        <Avatar message={props.message} />
      );
    name = !isSelf ? (
      <View className={styles.name}>{props.message.sender.name || "车友"}</View>
    ) : (
      <></>
    );
  } else if (props.message.type == 5) {
    if (!props.message.sender) {
      // @ts-ignore
      props.message.sender = {};
      props.message.sender.portrait =
        "https://img.jgrm.net/Fl35YFzCTR0lxkuDq0JeZir69Gms?"; // 交广领航logo
    }
    CustAvatar = <Avatar message={props.message} />;
    name = <View className={styles.name}>交广领航</View>;
  }

  switch (props.message.type) {
    case 0: // 文本
      item = (
        <View className={`${styles.messageItem} ${isSelf ? styles.right : ""}`}>
          {CustAvatar}
          <View className={styles.body}>
            {name}
            <Text content={props.message.content} />
          </View>
          {sending == "start" && LoadingFlag}
          {sending == "error" && sendError}
        </View>
      );
      break;
    case 1: // 语音
      item = (
        <View className={`${styles.messageItem} ${isSelf ? styles.right : ""}`}>
          {CustAvatar}
          <View className={styles.body}>
            {name}
            <View
              className={`${styles.content} ${styles.text} ${
                props.playStatus ? styles.playing : ""
              }`}
              onClick={() => {
                props.onPlayAudio(props.message.content);
              }}
            >
              {/* <Text content={`语音${props.message.speech}`} /> */}
              <Audio speech={props.message.speech} />
            </View>
          </View>
        </View>
      );
      break;
    case 2: // 图片
      item = (
        <View className={`${styles.messageItem} ${isSelf ? styles.right : ""}`}>
          {CustAvatar}
          <View className={styles.body}>
            {name}
            <ImageMsg content={props.message.content} />
          </View>
        </View>
      );
      break;
    case 5: // 关键词回复
      let reply = JSON.parse(props.message.replyContentJson || "");
      item =
        reply.xml.MsgType == "text" ? (
          <View className={styles.messageItem}>
            {CustAvatar}
            <View className={styles.body}>
              {name}
              <KeyWordReply content={reply.xml || {}} />
            </View>
          </View>
        ) : (
          <View className={`${styles.messageItem} ${styles.messageNews}`}>
            <KeyWordReply content={reply.xml || {}} />
          </View>
        );
      break;
    default:
  }
  return item;
};
export default React.memo(Index, (prevProps, nextProps) => {
  // 自定义对比方法，也可忽略不写
  if (
    prevProps.message.sending ||
    nextProps.message.sending ||
    prevProps.playStatus ||
    nextProps.playStatus ||
    prevProps.sessionId ||
    nextProps.sessionId
  ) {
    return false;
  } else {
    return true;
  }
  // return true // 则不重新渲染
  // return false // 重新渲染
});
// export default Index
