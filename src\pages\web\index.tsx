import Taro, { getCurrentInstance } from "@tarojs/taro";
import React, { Component } from "react";
import { WebView, View } from "@tarojs/components";
import {
  getWebViewURL,
  showToast,
  showModal,
  getURLSearchParams,
  getFullImagePath,
} from "@/tools";
import { deleteURLSearchParams } from "@/tools/url";
import {
  getLoggedIn,
  setLoggedIn,
  getLoginCount,
  getWebViewCallbackURL,
  setWebViewCallbackURL,
} from "@/gdata";

interface Props {}

interface State {
  pageURL: string;
  originURL: string;
  loggedIn: boolean;
  loginCount: number;
  data?: any;
  showWebView: boolean;
}

const EMPTY_PAGE = "about:blank";

export default class Index extends Component<Props, State> {
  $instance: any = getCurrentInstance();
  isWeapp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP;
  isAlipay = Taro.getEnv() === Taro.ENV_TYPE.ALIPAY;
  messageTimer: ReturnType<typeof setTimeout>;
  constructor(props) {
    super(props);
    this.state = {
      pageURL: EMPTY_PAGE,
      originURL: EMPTY_PAGE,
      loggedIn: getLoggedIn(),
      loginCount: getLoginCount(),
      data: {},
      showWebView: true, // 支付宝小程序刷新用，测试发现通过切换pageURL，在支付宝中不生效，暂通过此方式解决
    };
  }

  componentDidMount() {
    // 初始化分享参数
    let url = decodeURIComponent(this.$instance.router.params.url);
    // let url = this.$instance.router.params.url;
    if (this.isAlipay) {
      url = this.$instance.router.params.url;
    }
    if (this.$instance.router.params.q) {
      const params = getURLSearchParams(
        decodeURIComponent(this.$instance.router.params.q)
      );
      url = String(params.url);
      const shareData = {
        title: "",
        imgUrl: "",
      };
      if (params.title) {
        shareData.title = String(params.title);
      }
      if (params.img) {
        shareData.imgUrl = getFullImagePath(
          decodeURIComponent(String(params.img))
        );
      }
      this.setState({
        data: shareData,
      });
    }

    const webviewURL = getWebViewURL(url);
    this.setState({
      pageURL: webviewURL,
      originURL: getWebViewURL(url, false), // 原始地址，不带授权参数
    });
    // 页面需要登录且当前未登录时，跳转到登录页面去
    // console.log(this.$instance.router)
    // TODO: 跳转指定backurl
    const isLoggedIn = getLoggedIn();
    console.log("isLoggedIn:", isLoggedIn);
    const requireSignIn =
      this.$instance.router.params.requireSignIn == 1 && !isLoggedIn;
    if (requireSignIn) {
      console.log("nav to login");
      Taro.navigateTo({
        url: "/pages/login/index",
      });
    }
  }

  componentWillUnmount() {}

  // config: Config = {
  //   navigationBarTitleText: '交广领航',
  // }

  componentDidShow() {
    const isLoggedIn = getLoggedIn();
    const loginCount = getLoginCount();
    setLoggedIn(isLoggedIn);
    const callbackURL = getWebViewCallbackURL();
    // 刷新页面条件：
    // 1. 登录状态发生变化
    // 2. 登录状态可能发生变化（跳转到了登录页面后返回）
    // 3. 回调url参数不为空（收银台会设置callbackURL）
    const loginStateChanged = !this.state.loggedIn && isLoggedIn;
    const shouldRefreshPage =
      this.state.loginCount != loginCount ||
      loginStateChanged ||
      ((this.state.loginCount != loginCount || loginStateChanged) &&
        !!callbackURL);
    // console.log('componentDidShow:', this.$instance.router.params);
    // console.log('state:', this.state, 'loggedIn:', isLoggedIn);
    // console.log('shouldRefreshPage:', shouldRefreshPage);
    // debugger
    if (shouldRefreshPage) {
      this.setState({
        loggedIn: isLoggedIn,
        loginCount: loginCount,
        showWebView: false,
      });
      let url = getWebViewURL(this.state.originURL);
      if (callbackURL) {
        url = getWebViewURL(callbackURL);
        // 用完callbackURL，清除 callbackURL
        setWebViewCallbackURL("");
      }
      this.reloadPage(url);
    }
  }
  /**
   * 刷新页面
   * @param url
   */
  reloadPage(url) {
    // showToast('reloadPage...')
    // webview没有提供刷新方法，此处 先将url设为 empty_url，再设置webview的url可触发webview重新加载页面
    this.setState({
      pageURL: EMPTY_PAGE,
    });
    setTimeout(() => {
      this.setState({
        pageURL: url,
        showWebView: true,
      });
    }, 300);
  }
  onShareAppMessage(res) {
    // webview页面的分享地址设为 webview页面
    // console.log(res);
    let url = res.webViewUrl || this.state.originURL;
    // url中有token时，分享时去掉token参数
    if (url.indexOf("token") > -1) {
      url = deleteURLSearchParams(url, ["token"]);
    }
    let path = "/pages/web/index?url=" + encodeURIComponent(url);
    const couldShare = this.$instance.router.params.share != 0;

    if (!couldShare) {
      path = "/pages/index/index";
    }
    // console.log(path)
    // showToast(path);
    const shareInfo = {
      path: path,
      title: "交广领航",
      imageUrl: null,
    };
    const shareData = this.state.data;
    if (shareData) {
      if (shareData.title) {
        shareInfo.title = shareData.title;
      }
      // web页初始化时传递的分享信息链接link为-"http://www.jgrm.net/mobile/jglh.html"
      if (
        shareData.link &&
        shareData.link.indexOf("mobile/jglh.html") == -1 &&
        couldShare
      ) {
        shareInfo.path =
          "/pages/web/index?url=" + encodeURIComponent(shareData.link);
      }
      if (shareData.imgUrl) {
        shareInfo.imageUrl = shareData.imgUrl;
      }
    }
    const promise = new Promise((resolve) => {
      setTimeout(() => {
        const shareData = this.state.data;
        if (shareData) {
          if (shareData.title) {
            shareInfo.title = shareData.title;
          }
          // web页初始化时传递的分享信息链接link为-"http://www.jgrm.net/mobile/jglh.html"
          if (
            shareData.link &&
            shareData.link.indexOf("mobile/jglh.html") == -1 &&
            couldShare
          ) {
            shareInfo.path =
              "/pages/web/index?url=" + encodeURIComponent(shareData.link);
          }
          if (shareData.imgUrl) {
            shareInfo.imageUrl = shareData.imgUrl;
          }
        }
        resolve(shareInfo);
      }, 600); // 这里设置600是因为onWebViewMessage中的定时器是500
    });
    // showModal('提示', JSON.stringify(shareInfo) + JSON.stringify(shareData))
    return {
      ...shareInfo,
      promise,
    };
    // return { path: path };
  }

  componentDidHide() {}
  onWebViewError(e) {
    console.error(e);
  }
  onWebViewLoad(e) {
    console.info(e);
    const isLoggedIn = getLoggedIn();
    this.setState({
      loggedIn: isLoggedIn,
    });
  }
  onWebViewMessage(e) {
    // 清除之前的定时器
    clearTimeout(this.messageTimer);

    // 设置一个定时器，在短时间内没有新的消息时执行消息处理逻辑
    this.messageTimer = setTimeout(() => {
      let data = e.detail?.data;
      if (Array.isArray(e.detail.data)) {
        data = e.detail?.data?.slice(-1)[0];
      }
      // console.log('onWebViewMessage: ', data, e.detail.data);
      if (this.isAlipay) {
        switch (e.detail?.type) {
          case "shareConfig":
            this.setState({
              data: data,
            });
            break;

          case "navigateMiniProgram":
            // 支付宝小程序，跳转其他小程序
            Taro.navigateToMiniProgram({
              ...data,
              // envVersion: 'develop',
              success: function (res) {
                // 打开成功
              },
            });
            break;

          case "callAPI":
            // 支付宝小程序，调用API
            my[e.detail.api](data);
            break;
        }
      }
      if (this.isWeapp) {
        this.setState({
          data: data,
        });
      }
      // this.setState({
      //   data: data,
      // })
    }, 500); // 设置延迟时间，比如500毫秒
  }
  render() {
    // 频繁切换webview可能会导致小程序报错：同一个页面只能渲染一个webview
    const { showWebView } = this.state;
    return (
      /* this.state.pageURL === EMPTY_PAGE ?
      <View className='loading'>loading</View> : */
      (this.isWeapp || (this.isAlipay && showWebView)) && (
        <WebView
          onError={this.onWebViewError.bind(this)}
          onLoad={this.onWebViewLoad.bind(this)}
          onMessage={this.onWebViewMessage.bind(this)}
          src={this.state.pageURL}
        />
      )
    );
  }
}
