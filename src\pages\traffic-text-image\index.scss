.traffic-text-image-page {
  width: 100vw;
  height: 100vh;
  background: #f3f3f3
    linear-gradient(180deg, #ff7053 0%, rgba(254, 235, 221, 0) 40%);
  box-sizing: border-box;
  padding-bottom: 160px;
  .page-content {
    padding: 30px;
  }
  .location-wrap {
    background: #ffffff;
    border-radius: 20px;
    padding: 20px;
    display: flex;
    align-items: center;
    .icon {
      margin-right: 24px;
      color: #666666;
    }
    .location-text {
      flex: 1;
    }
    .location-input {
      font-size: 30px;
      color: #333333;
      // line-height: 46px;
      padding: 0;
    }
    .location-tips {
      margin-top: 8px;
      font-size: 24px;
      color: #333333;
      line-height: 40px;
    }
  }
  .card-wrap {
    background: #fff;
    padding: 20px;
    border-radius: 20px;
    margin-top: 20px;
    .card-title {
      font-size: 30px;
      color: #666666;
      line-height: 46px;
      margin-bottom: 20px;
    }
  }
  .traffic-types {
    background: #ffffff;
    border-radius: 20px;
    padding: 30px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 30px;
    margin-top: 20px;
    .traffic-type {
      // padding: 20px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
    .traffic-icon {
      padding: 50px;
      margin-bottom: 8px;
      background-color: #f3f4f6;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      overflow: hidden; /* 确保溢出的内容被隐藏，防止影响圆形 */
    }
    .traffic-type-icon {
      width: 62px;
      height: 62px;
      display: block;
    }
    .traffic-type-text {
      color: #999999;
      font-size: 28px;
    }
    .traffic-type-active {
      .traffic-icon {
        background-color: #fd4925;
      }
      .traffic-type-text {
        color: #fd4925;
      }
    }
  }
  .traffic-directions {
    display: flex;
    align-items: center;
    .traffic-direction {
      flex: 1;
      text-align: center;
      font-size: 28px;
      color: #666666;
      background: #f3f4f6;
      border-radius: 10px;
      margin-right: 20px;
      padding: 10px 0;
      &:last-child {
        margin-right: 0;
      }
    }
    .traffic-direction-active {
      background: #fd4925;
      color: #ffffff;
    }
  }
  .remark-input {
    // padding: 0;
    border: 1px solid #dddddd;
    width: 100%;
    min-height: 200px;
    padding: 20px;
    box-sizing: border-box;
    .van-field__control {
      padding: 20px;
    }
  }
  .traffic-image-wrap {
    position: relative;
    margin-top: 20px;
    .image-wrap {
      display: flex;
      align-items: flex-start;
      justify-content: center;
    }
  }
}
