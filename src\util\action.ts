// import {getReqId} from "@/api";
/**
 * @description: 组合发送聊天室消息体
 * @param {string} content 消息内容(文本消息语音/图片/小视频的资源id)
 * @param {number} type 消息类型值
 * @param {number} mtype 聊天场景（1：私信2：群聊）
 * @param {string} to 聊天室id
 * @param {number} speech 随机数，用于判断消息是否发送成功
 * @return {object}
 */
export async function newAction(content, type = 0, mtype = 2, to = 'HN0001', speech = -1) : Promise<any> {
  // const time = parseInt(((new Date()).getTime() / 1000).toFixed(0))
  const time = new Date().getTime() + ''
  // const res = await getReqId()
  return {
    content,
    type,
    mtype,
    to,
    speech,
    randomNumber: time
  }
}
/**
 * @description: 组合接收聊天室消息——关键字回复内容
 * @param {string} content 消息内容(文本消息语音/图片/小视频的资源id)
 * @return {object}
 */
export async function transKeywordReply(content) : Promise<any> {
  // const time = parseInt(((new Date()).getTime() / 1000).toFixed(0))
  const time = new Date().getTime()
  // const res = await getReqId()
  return {
    content,
    type: 0,
    sender: {
      portrait: '',
      name: '',
      uid: '',
    },
    id: new Date().getTime() + ''
  }
}
