module.exports = {
  env: {
    NODE_ENV: '"development"',
  },
  defineConstants: {
    BASE_URL: '"https://dev.jgrm.net"',
    WS_URL: '"wss://dev.jgrm.net/websocket.io/"',
    UPLOAD_QINIU_URL: '"https://upload.qiniup.com"', // 七牛的上传域名

    // BASE_URL: '"https://radio.jgrm.net"',
    // WS_URL: '"wss://radio.jgrm.net/websocket.io/"',
    // UPLOAD_QINIU_URL: '"https://upload.qiniup.com"', // 七牛的上传域名
  },
  compiler: {
    type: "webpack5",
    // 仅 webpack5 支持依赖预编译配置
    prebundle: {
      enable: false,
    },
  },
  mini: {
    enableSourceMap: false,
  },
  h5: {},
};
