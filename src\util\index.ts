export type ScrollElement = Element | Window
import { ITouchEvent } from '@tarojs/components'
import { TaroElement } from '@tarojs/runtime'
import { createSelectorQuery } from '@tarojs/taro'
import { OrderCategory } from '@/enums';
import dayjs from 'dayjs'

import relativeTime from 'dayjs/plugin/relativeTime';
import localeData from 'dayjs/plugin/localeData';

dayjs.extend(relativeTime);
dayjs.extend(localeData);

dayjs.extend(relativeTime);

export function isPhone(phone: string) {
  return /^1[123456789]\d{9}$/.test(phone)
}

/**
 * 解析url中hash值后面的参数
 * @param {*} localhash
 */
 export function getSearchParams(search) {
  let paramPart = search.split('?');
  let all = {};
  if (paramPart[1]) {
    let tmp = paramPart[1].split('&');
    tmp.map(item => {
      let aa = item.split('=');
      all[aa[0]] = aa[1];
    })
  }

  return all;
}

/**
 * 获取第三方app加载时传递的参数
 * @param {*} localhash
 */
export function getUrlHashParams():{session?} {
  let hash = window.location.hash;
  const params = getSearchParams(hash)
  return params;
}


/**
 * 根据资源获取图片url，支持七牛云图片id，或图片url，图片路径
 * 曾经有老版本app（3.8.x）预览图片不支持https协议的图片地址
 * @param {string} value 图片ID或路径
 * @param {string} type 可选，七牛云图片参数
 * @param {string} protocol 图片协议
 */
 export function getImageURL(value, type = '?', protocol = 'https:') {
  // console.log(...arguments);

  // file, data, bolb, http, https ...
  if (/^\w+:/.test(value)) return value;

  // `./` 开头 认为是相对路径
  if (/^\./.test(value)) return value;

  // `/` 开头认为是绝对路径
  if (/^\/\//.test(value)) return value;

  // `img/` 目录开头的，认为是相对路径
  if (/^img\//.test(value)) return value;

  // android某版本有些图片id是路径，如/storage/6633-6466/DCIM/Camera/20161002_154738.jpg
  if (/^\//.test(value) && !/storage/.test(value)) return value;

  // 其他值都认为是
  return `${protocol}//img.jgrm.net/${value}${type}`;
}

export function isValidURL(o) {
  return /^http(s)?:\/\/.+$/.test(o)
}

function selectorQuery(nodesRef?: TaroElement) {
  if (!nodesRef || nodesRef.nodeName === 'root') {
    return createSelectorQuery().selectViewport()
  } else {
    return createSelectorQuery().select('#' + nodesRef.uid)
  }
}
export function scrollOffset(nodesRef: TaroElement) {
  return new Promise<{
    scrollLeft: number // 节点的水平滚动位置
    scrollTop: number // 节点的竖直滚动位置
  }>((resolve) => {
    // 没有固定高度-- 就去拿page的高度
    if (process.env.TARO_ENV === 'h5') {
      const _nodesRef: any =
        nodesRef || document.documentElement || document.body
      return resolve({
        scrollLeft: _nodesRef.scrollLeft,
        scrollTop: _nodesRef.scrollTop,
      })
    }
    return selectorQuery(nodesRef)
      .scrollOffset()
      .exec((res) => {
        resolve(res[0])
      })
  })
}

export function boundingClientRect(nodesRef: TaroElement) {
  return new Promise<{
    /** 节点的下边界坐标 */
    bottom: number
    height: number
    /** 节点的 ID */
    id: string
    /** 节点的左边界坐标 */
    left: number
    /** 节点的右边界坐标 */
    right: number
    /** 节点的上边界坐标 */
    top: number
    /** 节点的宽度 */
    width: number
  }>((resolve) => {
    if (process.env.TARO_ENV === 'h5') {
      const _nodesRef: any =
        nodesRef || document.documentElement || document.body
      return resolve(_nodesRef.getBoundingClientRect())
    }
    return selectorQuery(nodesRef)
      .boundingClientRect()
      .exec((res) => {
        resolve(res[0])
      })
  })
}
export const stopPropagation = (event: ITouchEvent) => event.stopPropagation()

export function preventDefault(
  event: ITouchEvent,
  isStopPropagation?: boolean,
) {
  /* istanbul ignore else */
  // if (typeof event?.cancelable !== 'boolean' || event?.cancelable) {
  // }
  event.preventDefault()

  if (isStopPropagation) {
    stopPropagation(event)
  }
}

export const debounce = <T extends any[], R>(
  fn: (...args: T) => R,
  timeout: number,
) => {
  let handle = 0
  let lastArgs: T | null = null
  const ret = (...args: T) => {
    lastArgs = args
    clearTimeout(handle)
    handle = setTimeout(() => {
      lastArgs = null
      fn(...args)
    }, timeout) as unknown as number
  }
  ret.flush = (): R | void => {
    clearTimeout(handle)
    if (lastArgs) {
      const _lastArgs = lastArgs
      lastArgs = null
      return fn(..._lastArgs)
    }
  }
  // ret.cancel = () => {
  //   lastArgs = null
  //   clearTimeout(handle)
  // }
  return ret
}
/**
 * @description: 根据订单类型过滤订阅消息id
 * @param {*} msgs 消息对象
 * @param {*} type 订单类型
 * @return {*}
 */
export function filterSubscribeMessage(msgs = {}, type:string) {
  // msgs如下
  // {
  //   "refund_subscribe": "-CXblxmeMxqD_tTjBqBrkrSxY5_Olrhqo1CbYGTy1bU",
  //   "pay_success_subscribe": "5kP1GAj2cDkK9u47IS6cvoSXgdJRa6PKtk1gUpxG_CM",
  //   "send_subscribe": "BqxzuWLLJRZ97-SUUWwgerlqnBt1FuJdtr8wF9oU53Y",
  //   "sign_remind_subscribe": "IVNCtATcKHairezHs-mFB5CDYiAbX-ZQZlqwkNnJ-UU",
  //   "member_expire_subscribe": "s3l9mEABe90gAJdtt9I9XOrO5DFPUPFF-yA4sDj71wU",
  //   "coupon_expire_subscribe": "y28Zb2CtLsctP2P_SsE7Br7yNu0iI1KLGaEsDgtWDlo",
  //   "open_member_subscribe": "c5nR1y7Qc8PnC7FTqVNRE43WMVZL-XAG1zuZU4xDn0Q"
  // }
  let msgsKeys = Object.keys(msgs).filter((item) => {
    if (type == OrderCategory.VIP){
      return item == 'open_member_subscribe' || item == 'member_expire_subscribe'
    }
    if (type == OrderCategory.MALL){
      return item == 'send_subscribe' || item == 'pay_success_subscribe' || item == 'refund_subscribe'
    }
    if (type == 'coupons'){
      return item == 'coupon_expire_subscribe'
    }
    return false
  }) || []
  return msgsKeys.map((item) => {
    return msgs[item]
  })
}
