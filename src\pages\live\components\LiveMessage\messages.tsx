import React from "react";
import Taro from "@tarojs/taro";
import { View, Block } from "@tarojs/components";
import { Transition } from "@antmjs/vantui";
// import { getToken, setToken } from "@/util/auth";
import { isUserSelfMsg, isMsgExist, generateUserField } from "@/util/live";
import { isH5, isInWeApp, isWeapp, isIOS } from "@/util/env";
// import { set as setGlobalData, get as getGlobalData } from "@/util/globalData";
import { getLoggedIn, getSessionId } from "@/gdata";
import { liveModeType } from "@/enums";
import { getMessages, handleRead } from "@/api/modules/chat";
import { getAudioURL } from "@/tools";
import { scrollOffset, debounce } from "@/components/power-scroll/utils";
import styles from "./messages.module.less";

import SendContext from "./context";
import Input from "./Input";
import VantScrollView from "@/components/power-scroll";
import MessageItem from "../LiveMessage/MessageItem/index";

const pageSize = 10;

const Index: React.FC<{
  room: any;
  onlineCountChange: (value: string) => void;
  onLiveModeChange: (value: APP.Message) => void;
}> = (props) => {
  const [messages, setMessages] = React.useState<APP.Message[]>([]);

  const [isIphone, setIphone] = React.useState(false);
  const [noMore, setNoMore] = React.useState(false);

  const [keyboardHeight, setKeyboardHeight] = React.useState(0);

  const [socketTask, setTask] = React.useState<Taro.SocketTask | undefined>();

  // 控制滚动条滚动到底部
  const [toTop, setToTop] = React.useState(false);

  const [roomConfig, setRoomConfig] = React.useState<any>({});

  // const [socket, setSocket] = React.useState<any>([]);

  const scrollViewScrollTop = React.useRef<any>(0);

  const msgWrapRef = React.useRef<HTMLDivElement>();

  const sendMessageRef = React.useRef<APP.Message[]>([]);

  const heartbeatTimer = React.useRef<any>();

  const [audioContext, setAudioContext] = React.useState<any>();
  const [currentAudioUrl, setCurrentAudioUrl] = React.useState("");
  const currentAudio = React.useRef("");
  const sessionIdRef = React.useRef(getSessionId());

  const currentAudioPlayStatus = React.useRef(false);

  const flag = React.useRef(false);
  const socketId = React.useRef(0);
  const currentRoomId = React.useRef(9999);

  let onlineCountText = React.useCallback((count) => {
    let text = "";
    if (count) {
      if (count > 10000) {
        let formatCount = count / 10000;
        let formatCountRemainder = count % 10000;
        if (!formatCountRemainder) {
          text = `${formatCount}万 看过`;
        } else {
          text = `${formatCount.toFixed(2)}万 看过`;
        }
      } else {
        text = `${count} 看过`;
      }
    }
    // console.log('在线人数', text)
    return text;
  }, []);

  const send = React.useCallback(
    (act: any): Promise<boolean> => {
      return new Promise((resolve, reject) => {
        if (socketTask) {
          socketTask.send({
            data: JSON.stringify(act),
            success: () => {
              // 三种情况
              // 1. 正常发送消息
              // 将发送的消息合并至messages，并放入sendMessageRef中暂存
              // 当接收到广播消息时，如果消息存在sendMessageRef中，则不合并至messages中
              // 当接收收到消息回执，则将sendMessageRef中此条消息移除
              // 2.发送失败，重新发送
              // 发送消息时，通过isMsgExist判断此消息是否存在sendMessageRef中，如果存在则说明是重新发送
              // 此时只更改messages中sending状态值，及消息id，不进行后续逻辑
              // 3.发送心跳消息
              // 不进行后续操作
              if (act.mtype == 99) return;
              if (isMsgExist(act.randomNumber, sendMessageRef.current) > -1) {
                setMessages((prev) => {
                  let prevCopy = prev.slice();
                  let index = prevCopy.findIndex((item) => {
                    return item.randomNumber == act.randomNumber;
                  });
                  if (index > -1) {
                    prevCopy[index].id = new Date().getTime() + ""; // 触发消息组件重新渲染
                    prevCopy[index].sending = "start";
                  }
                  return prevCopy;
                });
                return;
              }
              sendMessageRef.current = sendMessageRef.current.concat([act]);
              act.id = act.randomNumber;
              act.sending = "start";
              act.sender = generateUserField(); // 生成发送人信息
              setMessages((prev) => {
                return [act].concat(prev);
              });
              // console.log('发送成功++', act)
              setToTop((prevState) => !prevState);
              resolve(true);
            },
            fail: (res) => {
              // Taro.showToast({
              //   icon: "none",
              //   title: res.errMsg
              // });
              Taro.showModal({
                title: "提示",
                content: "聊天服务器已断开",
                confirmText: "重新连接",
              }).then((res) => {
                if (res.confirm) {
                  Taro.reLaunch({
                    url: `/pages/live/live?roomId=${
                      props.room?.roomId || "HN0001"
                    }`,
                  });
                }
              });
              reject(res.errMsg);
            },
          });
        } else {
          Taro.showModal({
            title: "提示",
            content: "聊天服务器已断开",
            confirmText: "重新连接",
          }).then((res) => {
            if (res.confirm) {
              Taro.reLaunch({
                url: `/pages/live/live?roomId=${
                  props.room?.roomId || "HN0001"
                }`,
              });
            }
          });
          reject("服务器已断开");
        }
      });
    },
    [props.room?.roomId, socketTask]
  );

  const close = React.useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      if (isWeapp()) {
        setTask((prevState) => {
          // console.log('触发close关闭', prevState)
          if (prevState) {
            Taro.closeSocket()
              .then(() => {
                resolve();
              })
              .catch(() => {
                reject();
              });
          }
          return undefined;
        });
      }
      if (isH5()) {
        setTask((prevState) => {
          if (prevState) {
            prevState.ws.close();
            resolve();
          }
          return undefined;
        });
      }
    });
  }, []);

  const connect = React.useCallback(async () => {
    if (socketTask) {
      await close();
    }
    props.onlineCountChange("");
    Taro.connectSocket({
      url: `${WS_URL}?roomId=${
        props.room?.roomId || "HN0001"
      }&id=19&random=${new Date().getTime()}`,
      header: {
        xcxtoken: getSessionId(),
      },
      complete: (res) => {
        // console.log('socket complete回调成功', res)
      },
    }).then((socket) => {
      // console.log('创建socket链接')
      setTask(socket);

      socket.onOpen(() => {
        // console.log('socket onopen监听', socket)
      });

      socket.onMessage(function (result) {
        // console.log('socket onMessage内容：' + result.data)
        if (result.data != "") {
          // 接收消息
          try {
            /**
             * @description: // 判断广播消息是否已存在messages中，如果存在则更改其sending状态，并将sendMessageRef中此条消息删除
             * @param {object} msg 消息
             * @return {Array} 修改后的messages副本
             */
            function handleMsg(msg, msgList = messages) {
              // 修改发送状态字段
              let messagesCopy = msgList.slice();
              let modifyIndex = messagesCopy.findIndex((item) => {
                return item.randomNumber == msg.randomNumber;
              });
              if (modifyIndex > -1) {
                messagesCopy[modifyIndex].sending = "complete";
              }
              let delIndex = isMsgExist(
                msg.randomNumber,
                sendMessageRef.current
              );
              // 删除暂存的消息
              if (delIndex > -1) {
                let arr = sendMessageRef.current.slice();
                arr.splice(delIndex, 1);
                // setUserSendMessage(arr)
                sendMessageRef.current = arr;
              }
              return messagesCopy;
            }
            const action: APP.Action = JSON.parse(result.data);
            // if(action.code == 401){
            //   return
            // }
            switch (action.data.type) {
              case 0:
              case 1:
              case 2:
              case 4: {
                const msg = action.data as APP.Message;
                // console.log('sendMessageRef.current++', sendMessageRef.current)
                if (isMsgExist(msg.randomNumber, sendMessageRef.current) > -1) {
                  return;
                }
                setMessages((prev) => {
                  return [msg].concat(prev);
                });
                if (scrollViewScrollTop.current < 60) {
                  setToTop((prevState) => !prevState);
                }
                break;
              }
              case 5: {
                // 关键词回复，需要自己补充content、头像、id等
                let msg = action.data as APP.Message;
                setMessages((prev) => {
                  let _prevMsgs = handleMsg(msg, prev);
                  return [msg].concat(_prevMsgs);
                });
                if (scrollViewScrollTop.current < 60) {
                  setToTop((prevState) => !prevState);
                }
                break;
              }
              case 6: {
                // 消息回执
                let msg = action.data as APP.Message;
                // 修改消息的loading状态
                // setMessages(handleMsg(msg))
                setMessages((prev) => {
                  let _prevMsgs = handleMsg(msg, prev);
                  return _prevMsgs;
                });
                break;
              }
              case 7: {
                // 聊天室模式修改后系统通知消息
                let config = action.data as APP.Message;
                setRoomConfig((prevConfig) => {
                  return {
                    ...prevConfig,
                    ...config,
                  };
                });
                break;
              }
              case 8: {
                // 用户关进小黑屋的通知消息
                let config = action.data as APP.Message;
                setRoomConfig((prevConfig) => {
                  return {
                    ...prevConfig,
                    ...config,
                  };
                });
                break;
              }
              case 9: {
                // 连接成功后给用户返回的消息（聊天室模式、在线人数和用户是否在黑名单中)
                let config = action.data as APP.Message;
                setRoomConfig(config);
                break;
              }
              case 12: {
                // 显示多少人看过
                let config = action.data as APP.Message;
                // console.log('类型12返回数据', config)
                if (config.onlineCount) {
                  let text = onlineCountText(config.onlineCount);
                  props.onlineCountChange(text);
                }
                break;
              }
            }
            // 直播结束需要停止直播源播放，并跳转到直播数据页面
            if (!!action.data.liveMode) {
              props.onLiveModeChange(action.data);
            }
          } catch (e) {}
        }
      });
      socket.onError((e) => {
        console.log("socketTask出错", e);
        setTask(undefined);
        Taro.showToast({
          title: "连接服务器失败",
          icon: "none",
        });
      });
      socket.onClose((res) => {
        console.log("监听socketTask关闭成功", res);
        // setTask(undefined);
      });
    });
  }, [close, messages, onlineCountText, props, socketTask]);

  // 对应 onShow
  Taro.useDidShow(() => {
    // console.log('对应 onShow++', getSessionId())
    if (sessionIdRef.current != getSessionId()) {
      init();
      sessionIdRef.current = getSessionId();
    }
    // setSessionId(getSessionId())
  });

  const handlePlayStatus = React.useCallback(
    (msg: APP.Message) => {
      if (
        msg.type == 1 &&
        msg.content == currentAudioUrl &&
        currentAudioPlayStatus.current
      ) {
        return true;
      }
      return false;
    },
    [currentAudioUrl]
  );
  React.useEffect(() => {
    const innerAudioText = Taro.createInnerAudioContext();
    setAudioContext(innerAudioText);

    setIphone(() => {
      const model = Taro.getSystemInfoSync().model;
      return /iphone/i.test(model);
    });
  }, []);
  const playAudio = React.useCallback(
    (url) => {
      if (url && url === currentAudio.current) {
        if (currentAudioPlayStatus.current) {
          audioContext.pause();
          setCurrentAudioUrl("");
        } else {
          audioContext.play();
          currentAudioPlayStatus.current = true;
          setCurrentAudioUrl(url);
        }
      } else {
        audioContext && audioContext.stop();
        // audioContext.autoplay = true;
        audioContext.src = getAudioURL(url);
        // 当音频可以播放就将状态从loading变为可播放
        if (isIphone) {
          // IOS环境需要立即调用
          audioContext.play();
        } else {
          audioContext.onCanplay(() => {
            console.log("onCanplay");
            audioContext.play();
          });
        }
        // 当音频在缓冲时改变状态为加载中
        audioContext.onWaiting(() => {
          console.log("onWaiting");
        });
        // 开始播放后更改图标状态为播放中
        audioContext.onPlay(() => {
          console.log("onPlay");
          currentAudio.current = url;
          currentAudioPlayStatus.current = true;
          setCurrentAudioUrl(url);
        });
        // 暂停后更改图标状态为暂停
        audioContext.onPause(() => {
          console.log("onPause");
          // currentAudio.current = "";
          // setCurrentAudioUrl('')
          currentAudioPlayStatus.current = false;
        });
        // 播放结束后更改图标状态
        audioContext.onEnded(() => {
          // console.log("onEnded");
          currentAudio.current = "";
          currentAudioPlayStatus.current = false;
          setCurrentAudioUrl("");
        });
        // 播放出错
        audioContext.onError((e) => {
          // currentAudio.current = "";
          // currentAudioPlayStatus.current = false
          // setCurrentAudioUrl('')
        });
      }
    },
    [audioContext, isIphone]
  );

  const getMoreMessage = React.useCallback(async () => {
    if (!noMore && !flag.current) {
      if (messages.length > 0) {
        flag.current = true;
        const id = messages[messages.length - 1].id;
        const t = messages[messages.length - 1].t; // 时间戳
        if (id) {
          try {
            const res = await getMessages(
              props.room?.roomId || "HN0001",
              t,
              pageSize
            );
            setMessages((prevState) => {
              flag.current = false;
              // console.log('子组件ref', messageContentViewRef.current)
              return [...prevState.concat(res)];
            });
            // console.log('父组件修改messages')
            if (res.length < pageSize) {
              setNoMore(true);
            }
          } catch (e) {}
        }
      }
    }
  }, [messages, noMore, props.room]);

  React.useEffect(() => {
    if (socketTask) {
      // 心跳
      if (heartbeatTimer.current !== -1) {
        clearInterval(heartbeatTimer.current);
        heartbeatTimer.current = -1;
      }
      heartbeatTimer.current = setInterval(() => {
        // console.log('heartbeatTimer心跳保活', socketTask.socketTaskId)
        // 心跳保活
        if (socketTask?.readyState !== 1) {
          // warning('socket链接还未准备好');
          return;
        }
        const options = {
          content: "心跳内容",
          mtype: 99,
        };
        send(options);
        // sendMessage('heartBeat', 'ping');
      }, 5000) as unknown as number;
    }
    return () => {
      clearInterval(heartbeatTimer.current);
      heartbeatTimer.current = -1;
    };
  }, [send, socketTask]);

  const init = React.useCallback(() => {
    Taro.showLoading({
      title: "连接中...",
    });
    setNoMore(false);
    setMessages([]);
    flag.current = false;
    getMessages(props.room?.roomId || "HN0001", new Date().getTime(), pageSize)
      .then((res) => {
        Taro.hideLoading();
        if (res.length < pageSize) {
          setNoMore(true);
        }

        setMessages(res);
        connect();
      })
      .catch((err) => {
        Taro.hideLoading();
      });
  }, [connect, props.room]);

  React.useEffect(() => {
    // console.log('sessionId++', sessionIdRef.current)
    if (props.room?.id && currentRoomId.current != props.room?.id) {
      currentRoomId.current = props.room.id;
      init();
    }
    // console.log(props.room)
    // return () => {
    //   close();
    // };
  }, [init, props.room]);

  // 提前把reachTopRef.current的值 求出来
  const debounceScrollOffset = React.useMemo(() => {
    const _getScrollTop = async (e) => {
      const _scrollTop = e.detail.scrollTop;
      scrollViewScrollTop.current = _scrollTop;
      return _scrollTop;
    };
    return debounce(_getScrollTop, 200);
  }, []);

  let onScroll = React.useCallback(
    (e) => {
      debounceScrollOffset(e);
    },
    [debounceScrollOffset]
  );
  let keyboardHeightChange = React.useCallback((res) => {
    setKeyboardHeight(res.height || 0);
    if (res.height) {
      Taro.hideTabBar({
        animation: false,
      });
    } else {
      Taro.showTabBar({
        animation: false,
      });
    }
  }, []);
  React.useEffect(() => {
    Taro.onKeyboardHeightChange(keyboardHeightChange);
    return () => {
      Taro.offKeyboardHeightChange(keyboardHeightChange);
    };
  }, [keyboardHeightChange]);
  const cusStyles = React.useMemo(() => {
    // if (isH5() && !isIOS()) {
    // if (isH5()) {
    //   return {
    //     height: window.innerHeight + "px"
    //   }
    // }
    if (isWeapp()) {
      return {
        bottom: keyboardHeight + "px",
      };
    }
    return {};
  }, [keyboardHeight]);

  return (
    <SendContext.Provider value={send}>
      <View className={styles.scrollContainer}>
        <View className={styles.scrollContainerContent}>
          <View className={styles.channelTitle}>
            <View className={styles.channelTitleTag}>互动</View>
            <View className={styles.channelTitleText}>FM1041河南交通广播</View>
          </View>
          <View ref={msgWrapRef} className={styles.messageContent}>
            <VantScrollView
              // pullingText="下拉加载更多"
              // loosingText="释放加载更多"
              scrollTop={toTop ? 0.1 : 0.2}
              className={styles.vantScroll}
              finishedText="没有更多了"
              // onScrollToUpper={getMoreMessage}
              onScrollToLower={getMoreMessage}
              current={messages.length}
              finished={noMore}
              refresherEnabled={false}
              onScroll={onScroll}
              showScrollbar={!isIphone}
              // scrollAnchoring
              enableFlex
              enhanced
            >
              {messages.map((v, i) => {
                return (
                  <MessageItem
                    message={v}
                    key={v.id || v.msgId || v.randomNumber}
                    onPlayAudio={playAudio}
                    playStatus={handlePlayStatus(v)}
                    sessionId={sessionIdRef.current}
                  />
                );
              })}
            </VantScrollView>
          </View>
          {/* <Input className={styles.inputWrap} keyboardHeightChange={keyboardHeightChange} /> */}
          <Block>
            <Transition
              className={styles.inputWrapFixed}
              duration={{ enter: 300, leave: 1000 }}
              style={cusStyles}
              show
              name="fade-up"
            >
              <Input
                roomConfig={roomConfig}
                room={props.room}
                keyboardHeightChange={keyboardHeightChange}
              />
            </Transition>
          </Block>
        </View>
      </View>
    </SendContext.Provider>
  );
};

export default Index;
