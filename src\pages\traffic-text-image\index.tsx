import React, { Component } from "react";
import Taro, { getCurrentInstance } from "@tarojs/taro";
import { View, Text, Image, Textarea } from "@tarojs/components";
import SendBtn from "@/pages/traffic-voice/components/SendBtn";
// import { Dialog as VDialog, Toast, Checkbox, Button } from '@antmjs/vantui'
import { pushWebView, getAudioURL } from "@/tools";
import { getUserInfo } from "@/gdata";
import { getImageURL } from "@/util";
import { getTrafficMsgs, reportTraffic } from "@/api/modules/traffic";
import PrizePopup from "@/components/prize-popup";

import { Icon, Field, Popup } from "@antmjs/vantui";
import "./index.scss";

type SubmitParams =
  | {
      msgType: number;
      x: any;
      y: any;
      roadname: string;
      image?: string | undefined;
      type: number;
      direction: string;
      text: string;
      ptype: string;
    }
  | undefined;

interface Props {}

type QiNiuResponse = {
  key: string;
  hash: string;
};
interface State {
  showMap: boolean;
  message?: any;
  address: string;
  trafficTypes: any[];
  trafficDirections: any[];
  remark: string;
  type: string;
  currentMarker?: any;
  imageKey?: string;
  imgUploadToken?: string;
  showPrizePopup: boolean;
  prize?: any;
}

export default class Index extends Component<Props, State> {
  $instance: any = getCurrentInstance();

  // 实例变量，不参与页面更新
  currentAudio = ""; // 当前音频url
  constructor(props) {
    super(props);
    this.state = {
      showMap: false,
      address: "",
      remark: "", // 备注
      type: "", // text:文字，image:图片
      imageKey: "", // image:图片七牛key
      imgUploadToken: "", // 七牛上传token
      message: null, // 语音消息
      trafficTypes: [
        {
          icon: require("./images/icon01.png"),
          text: "通畅",
          value: 1,
          checked: false,
          iconChecked: require("./images/icon01_active.png"),
        },
        {
          icon: require("./images/icon02.png"),
          text: "拥堵",
          value: 2,
          checked: false,
          iconChecked: require("./images/icon02_active.png"),
        },
        {
          icon: require("./images/icon03.png"),
          text: "事故",
          value: 3,
          checked: false,
          iconChecked: require("./images/icon03_active.png"),
        },
        {
          icon: require("./images/icon04.png"),
          text: "故障",
          value: 6,
          checked: false,
          iconChecked: require("./images/icon04_active.png"),
        },
        {
          icon: require("./images/icon05.png"),
          text: "施工",
          value: 4,
          checked: false,
          iconChecked: require("./images/icon05_active.png"),
        },
        {
          icon: require("./images/icon06.png"),
          text: "管制",
          value: 7,
          checked: false,
          iconChecked: require("./images/icon06_active.png"),
        },
      ],
      trafficDirections: [
        {
          text: "东向西",
          value: 1,
          checked: false,
        },
        {
          text: "南向北",
          value: 2,
          checked: false,
        },
        {
          text: "北向南",
          value: 3,
          checked: false,
        },
        {
          text: "西向东",
          value: 6,
          checked: false,
        },
      ],
      currentMarker: null, // 当前标记的位置(经纬度、详细地址)
      showPrizePopup: false, // 上报完路况是否显示中奖弹窗
      prize: null, // 中奖信息
    };
  }

  toPage() {
    // pushWebView(this.state.url);
  }

  componentDidMount() {
    let that = this;
    let type = that.$instance.router.params.type;
    let image = that.$instance.router.params.image;
    let imgUploadToken = that.$instance.router.params.token;
    let marker = decodeURIComponent(that.$instance.router.params.marker);
    if (type == "image") {
      that.setState({
        type,
        imageKey: image,
        imgUploadToken,
      });
    } else {
      that.setState({
        type,
      });
    }

    // 接受上个页面传递的参数
    if (marker) {
      let markerObj = JSON.parse(marker);
      that.setState({
        currentMarker: markerObj,
        address: markerObj.address,
      });
    }
  }

  fetchMsgs(params) {
    return getTrafficMsgs(params).then((res) => {
      return res || [];
    });
  }

  setAddressValue(value) {
    const { detail } = value;
    this.setState({
      address: detail || "",
    });
  }

  setRemarkValue(e) {
    // const { detail } = e;
    const { value } = e.detail;
    this.setState({
      remark: value || "",
    });
  }

  toPrizePage(url) {
    if (!url) {
      return;
    }
    const params = [`url=${encodeURIComponent(url)}`, `share=0`].join("&");
    const path = `/pages/web/index?${params}`;
    Taro.redirectTo({
      url: path,
    });
  }

  formatParams(): SubmitParams {
    let {
      address,
      type,
      trafficTypes,
      trafficDirections,
      remark,
      currentMarker,
      imageKey,
    } = this.state;
    if (type == "text") {
      let trafficType = trafficTypes.find((item) => item.checked);
      let trafficDirection = trafficDirections.filter((item) => item.checked);
      let params = {
        msgType: 0, // 0:文字，1:图片，2:语音
        x: currentMarker.longitude,
        y: currentMarker.latitude,
        roadname: address,
        type: trafficType?.value,
        direction:
          trafficDirection.length == 4
            ? "四个方向"
            : trafficDirection.map((item) => item.text).join(""),
        text: remark,
        ptype: "", // 语音、文字路况时传空，图片路况时传TRAFFIC
      };
      return params;
    }
    if (type == "image") {
      let trafficDirection = trafficDirections.filter((item) => item.checked);
      let params = {
        msgType: 1, // 0:文字，1:图片，2:语音
        x: currentMarker.longitude,
        y: currentMarker.latitude,
        roadname: address,
        image: imageKey,
        type: 5, // 5:图片
        direction:
          trafficDirection.length == 4
            ? "四个方向"
            : trafficDirection.map((item) => item.text).join(""),
        text: remark,
        ptype: "TRAFFIC", // 语音、文字路况时传空，图片路况时传TRAFFIC
      };
      return params;
    }
  }

  handleReportSubmit() {
    let { type } = this.state;
    const params = this.formatParams();
    if (type == "text" && !params?.type) {
      Taro.showToast({
        title: "请选择路况类型",
        icon: "none",
      });
      return;
    }
    if (!params?.direction) {
      Taro.showToast({
        title: "请选择路况方向",
        icon: "none",
      });
      return;
    }
    reportTraffic(params).then((res) => {
      Taro.showToast({
        title: "上报成功",
        icon: "none",
      });
      if (res.prize?.url !== "" && res.prize?.thumb) {
        this.setState({
          showPrizePopup: true,
          prize: res.prize,
        });
      } else {
        Taro.navigateBack();
      }
    });
  }
  handleChangeImage() {
    const that = this;
    const { imgUploadToken } = this.state;
    Taro.chooseMedia({
      count: 1,
      mediaType: ["image"],
      success: (res) => {
        res.tempFiles.forEach((file) => {
          Taro.showLoading({
            title: "请稍等...",
          });
          Taro.uploadFile({
            url: UPLOAD_QINIU_URL,
            filePath: file.tempFilePath,
            name: "file",
            formData: { token: imgUploadToken },
          })
            .then((r) => {
              Taro.hideLoading();
              const result: QiNiuResponse = JSON.parse(r.data);
              if (result.key) {
                that.setState({
                  imageKey: result.key,
                });
              } else {
                Taro.showToast({
                  title: "上传失败",
                  icon: "none",
                });
              }
            })
            .catch((e) => {
              Taro.hideLoading();
              Taro.showToast({
                title: "上传失败",
                icon: "none",
              });
            });
        });
      },
    });
  }
  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  render() {
    let {
      address,
      trafficTypes,
      trafficDirections,
      remark,
      type,
      imageKey,
      currentMarker,
      showPrizePopup,
      prize,
    } = this.state;
    return (
      <View className="traffic-text-image-page">
        <View className="page-content">
          <View className="location-wrap">
            <Icon name="location-o" size="32px" className="icon" />
            <View className="location-text">
              <Field
                className="location-input"
                value={address}
                type="textarea"
                autosize={{ minHeight: "48rpx" }}
                placeholder="请输入地址"
                border={false}
                onChange={this.setAddressValue.bind(this)}
              />
              <Text className="location-tips">
                {currentMarker?.province}-{currentMarker?.city}-
                {currentMarker?.district}
              </Text>
            </View>
          </View>
          {type == "image" && (
            <View className="traffic-image-wrap">
              {!!imageKey && (
                <View
                  className="image-wrap"
                  onClick={this.handleChangeImage.bind(this)}
                >
                  <Image
                    src={getImageURL(imageKey)}
                    className="traffic-image"
                    mode="aspectFit"
                  />
                </View>
              )}
            </View>
          )}
          {type == "text" && (
            <View className="traffic-types">
              {trafficTypes.map((item) => {
                return (
                  <View
                    key={item.value}
                    className={`traffic-type ${
                      item.checked ? "traffic-type-active" : ""
                    }`}
                    onClick={() => {
                      // 如果是选中状态，就取消选中，否则就选中，并取消其他选中状态
                      if (item.checked) {
                        item.checked = false;
                      } else {
                        trafficTypes.forEach((item) => {
                          item.checked = false;
                        });
                        item.checked = true;
                      }
                      this.setState({
                        trafficTypes,
                      });
                    }}
                  >
                    <View className="traffic-icon">
                      <Image
                        src={item.checked ? item.iconChecked : item.icon}
                        className="traffic-type-icon"
                        mode="aspectFit"
                      />
                    </View>
                    <Text className="traffic-type-text">{item.text}</Text>
                  </View>
                );
              })}
            </View>
          )}
          <View className="card-wrap">
            <View className="card-title">路况方向:(可多选)</View>
            <View className="traffic-directions">
              {trafficDirections.map((item) => {
                return (
                  <View
                    key={item.value}
                    className={`traffic-direction ${
                      item.checked ? "traffic-direction-active" : ""
                    }`}
                    onClick={() => {
                      item.checked = !item.checked;
                      this.setState({
                        trafficDirections: [...trafficDirections],
                      });
                    }}
                  >
                    {item.text}
                  </View>
                );
              })}
            </View>
          </View>
          <View className="card-wrap">
            <View className="card-title">描述一下：</View>
            <Textarea
              className="remark-input"
              value={remark}
              autoHeight
              placeholder="以上无法描述你遇到的情况，没关系，在这里描述一下吧"
              onInput={this.setRemarkValue.bind(this)}
            />
          </View>
        </View>
        <SendBtn
          type="send"
          reportSubmit={this.handleReportSubmit.bind(this)}
        />
        <PrizePopup
          show={showPrizePopup}
          prizeImage={prize?.thumb || ""}
          onClose={() => {
            this.setState({ showPrizePopup: false });
            Taro.navigateBack();
          }}
          onImageClick={() => this.toPrizePage(prize?.url)}
        />
      </View>
    );
  }
}
