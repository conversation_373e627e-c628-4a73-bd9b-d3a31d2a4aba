import Taro, { getCurrentInstance } from "@tarojs/taro";
import React, { Component } from "react";
import { View, Image, Text, Input, Button } from "@tarojs/components";
import {
  showLoading,
  showModal,
  showToast,
  hideLoading,
  pushWebView,
} from "@/tools";
import * as Pages from "@/env/pages";
import {
  setLoggedIn,
  getUserType,
  setWebViewCallbackURL,
  addLoginCount,
  setUserInfo,
} from "@/gdata";
import { getUserInfo } from "@/api/modules/user";
import { UserType } from "@/enums";
import "./index.scss";
import AdPopup from "@/components/ad-popup";
import PrivacyPopup from "../../components/privacy-popup";

import {
  getSNSCode,
  getCheckUser,
  authLogin,
  authLoginV2,
  authLoginMerge,
  loginForCheZhuKa,
  requestAuth,
} from "../../api/modules/auth";
import { getAlertMessages, markAlertMessagesRead } from "@/api/modules/home";

enum LoginType {
  Normal = "normal",
  Quick = "quick",
  CheZhuKa = "chezhuka",
}

function cacheWechatUserInfo(info) {
  Taro.setStorage({
    key: "wechatUserInfo",
    data: JSON.stringify(info),
  });
}

function getCachedWechatUserInfo() {
  const info = Taro.getStorageSync("wechatUserInfo");
  const data = info ? JSON.parse(info) : null;
  return data || null;
}

export default class Index extends Component<any, any> {
  // config: Config = {
  //   navigationBarTitleText: '登录',
  // }
  $instance: any = getCurrentInstance();
  _count_down_code: any;
  hasRequestAuth: boolean = false;
  showModalPop: any = null;
  constructor(props: any) {
    super(props);
    const userType = getUserType();
    const isWeapp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP;
    const isAlipay = Taro.getEnv() === Taro.ENV_TYPE.ALIPAY;
    this.state = {
      isNewUser: false, // 是否是新用户
      phone: "", // 用户手机号
      isSend: false, // 是否发送过验证码Taro.getEnv()
      time: 60,
      seconds: 0,
      passWord: "", // 密码
      vcode: "", // 验证码
      isOpened: true,
      wxUserInfo: null,
      loginType:
        userType === UserType.CheZhuKa ? LoginType.CheZhuKa : LoginType.Quick,
      userType: userType,
      userInfo: getCachedWechatUserInfo(),
      privacyAgreeStatus: false, // 是否同意隐私政策
      showPrivacyPop: true, // 是否显示隐私政策弹窗
      isWeapp: isWeapp,
      isAlipay: isAlipay,
      popupInfo: {},
      showAdPop: false,
    };
  }
  componentWillMount() {
    // debugger
    // console.log('getLaunchOptionsSync: ', Taro.getLaunchOptionsSync())
    const backURL = this.$instance.router.params.backurl;
    // debugger
    if (backURL) {
      Taro.setStorageSync("pageType", "login");
      // Taro.setStorageSync('bacUrl', backURL);
      setWebViewCallbackURL(decodeURIComponent(backURL));
    }

    if (!this.state.privacyAgreeStatus && this.state.isWeapp) {
      this.setState({
        showPrivacyPop: true,
      });
      return;
    }
    this.hasRequestAuth = true;
    requestAuth(true)
      .then(() => {
        this.onLoginFinish();
      })
      .catch((err) => {
        console.error(err);
      });

    // this.checkUserBindStatus();
  }

  componentDidMount() {
    // this.startCountDown()
  }

  componentWillUnmount() {}
  private timer: any;
  componentDidShow() {}

  componentDidHide() {}

  toAgreement() {
    const url = Pages.USER_AGREEMENT;
    pushWebView(url);
  }

  // 获取验证码
  getVerify() {
    var phone = this.state.phone;
    console.log(phone);
    if (phone.length <= 0) {
      showModal("提示", "请输入手机号码");
      return;
    }
    if (phone.length != 11) {
      showModal("提示", "请输入正确的手机号码");
      return;
    }
    showLoading("正在发送....");

    Promise.all([getSNSCode(phone), getCheckUser(phone)])
      .then((res) => {
        hideLoading();
        this.setState(
          {
            isNewUser: this.state.isAlipay ? false : !res[1].status, // 支付宝小程序端不需要判断是否新用户
            isSend: !this.state.isSend,
          },
          () => {
            this.startCountDown();
          }
        );
      })
      .catch((errs) => {
        hideLoading();
        showToast(errs.toString());
      });
  }
  startCountDown() {
    this.stopCountDown();
    this.setState({
      seconds: 60,
    });
    this._count_down_code = setInterval(() => {
      let seconds = this.state.seconds;
      const value = --seconds;
      // console.log(this.state)
      if (value >= 0) {
        this.setState({
          seconds: value,
        });
      } else {
        this.stopCountDown();
      }
    }, 1000);
  }
  stopCountDown() {
    this._count_down_code && clearInterval(this._count_down_code);
  }
  // 提交数据
  loginForJglh() {
    let verifyCode = this.state.vcode;
    let phoneNum = this.state.phone;
    let wxUserInfo = this.state.wxUserInfo;
    let passWord = this.state.passWord;
    let passView = this.state.isNewUser;
    if (!phoneNum) {
      showModal("提示", "请输入手机号码");
      return;
    }

    if (phoneNum.length != 11) {
      showModal("提示", "请输入正确的手机号码");
      return;
    }

    if (!verifyCode) {
      showModal("提示", "请输入短信验证码");
      return;
    }

    if (passView && passWord == "") {
      showModal("提示", "请输入密码");
      return;
    }
    // debugger
    showLoading();
    authLogin(phoneNum, passWord, verifyCode, wxUserInfo)
      .then(() => {
        hideLoading();
        this.onLoginFinish();
      })
      .catch((err) => {
        hideLoading();
        showModal("提示", err.toString());
      });
  }
  // 提交数据
  loginForCheZhuKa() {
    const { userPhone } =
      Taro.getLaunchOptionsSync().referrerInfo?.extraData?.userPhone;
    // tip: 手机号可能带国际区号如+86开头
    if (!userPhone || userPhone.length < 11) {
      showModal("提示", `用户信息不完整！(${userPhone})`);
      return;
    }
    let phoneNum = userPhone;
    let wxUserInfo = this.state.wxUserInfo;
    showLoading();
    loginForCheZhuKa(phoneNum, wxUserInfo)
      .then(() => {
        hideLoading();
        this.onLoginFinish();
      })
      .catch((err) => {
        hideLoading();
        showModal("提示", err.toString());
      });
  }
  // type参数为silent时，静默登录，为manual时，手动登录
  onLoginFinish(type = "silent") {
    requestAuth()
      .then(getUserInfo)
      .then((info) => {
        setUserInfo(info);
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        if (type === "manual" && this.state.isWeapp) {
          this.getGlobalAlertMessage()
            .then((res) => {
              if (res?.url && res?.imageId && !this.state.isAlipay) {
                this.setState({
                  popupInfo: res,
                  showAdPop: true,
                });
              } else {
                Taro.navigateBack();
              }
            })
            .catch(() => {
              Taro.navigateBack();
            });
          return;
        }
        Taro.navigateBack();
      });
    setLoggedIn(true);
    addLoginCount();
    showToast("登录成功");
    // Taro.navigateBack();
  }
  getGlobalAlertMessage() {
    // 逻辑代码，获取全局弹窗提醒信息
    return getAlertMessages().then((res) => {
      // if (res && res.length > 0) {
      //   return res[0];
      // }
      return res;
    });
  }
  onFormInput(name, e) {
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.setState({
        [name]: e.detail.value,
      });
    }, 100);
  }
  onNormalLogin(userInfo) {
    if (this.state.isAlipay) {
      this.loginForJglh();
      return;
    }
    const info = userInfo || this.state.userInfo;
    if (info) {
      const userType = getUserType();
      if (userType == UserType.Jglh) {
        this.loginForJglh();
      } else if (userType == UserType.CheZhuKa) {
        this.loginForCheZhuKa();
      }
    }
  }
  onGetUserProfile(type, e) {
    const couldUseGetUserProfile = Taro.canIUse("getUserProfile");
    const needTwiceClick = couldUseGetUserProfile;
    console.log(type, e);
    console.log("couldUseGetUserProfile", couldUseGetUserProfile);
    const onSuccess = (info) => {
      console.log(info);
      const userInfo = info.userInfo;
      this.setState(
        {
          userInfo: userInfo,
          wxUserInfo: userInfo,
        },
        () => {
          if (type === LoginType.Quick && needTwiceClick) {
            showModal("提示", "请再点一次【登录】按钮");
          } else {
            this.onNormalLogin(userInfo);
          }
        }
      );
      cacheWechatUserInfo(userInfo);
    };
    if (couldUseGetUserProfile) {
      return Taro.getUserProfile({
        desc: "用户完善会员资料",
        success: onSuccess,
        fail: (err) => {
          console.error(err);
        },
      });
    }
    return Taro.getUserInfo({
      success: onSuccess,
    });
  }
  onGetPhoneNumber(res) {
    // showToast('登录成功');
    // Taro.switchTab({
    //   url: '/pages/index/index'
    // })
    if (!this.state.privacyAgreeStatus && this.state.isWeapp) {
      this.setState({
        showPrivacyPop: true,
      });
      return;
    }
    // 支付宝手机号绑定流程
    if (this.state.isAlipay) {
      this.onGetPhoneNumberFunc(res);
      return;
    }
    // 微信手机号绑定流程
    // 错误码（失败时返回）
    if (res.detail.errno) {
      showModal("提示", "请点击手机号登录/注册");
      return;
    }
    const { iv, encryptedData } = res.detail;
    if (!iv) return;
    // console.log('Taro.getUserProfile:', Taro.getUserProfile)
    showLoading();
    const userInfo = JSON.stringify(this.state.userInfo);
    authLoginMerge({ ...res.detail, userInfo })
      .then(() => {
        hideLoading();
        this.onLoginFinish("manual");
      })
      .catch((err) => {
        hideLoading();
        console.error(err);
        showModal("提示", err.toString());
      });
    // console.log(iv, encryptedData);
    // debugger
  }
  onGetPhoneNumberFunc(e) {
    my.getPhoneNumber({
      success: async (res) => {
        let result = JSON.parse(res.response);
        if (!result || result.code) {
          showModal("提示", `${result.code}, 稍后再试`);
          return;
        }
        let params: any = {
          encryptedData: res.response,
          code: "",
        };
        showLoading();
        authLoginMerge({ ...params })
          .then(() => {
            hideLoading();
            this.onLoginFinish("manual");
          })
          .catch((err) => {
            hideLoading();
            showModal("提示", err.toString());
          });
        // // 需要auth_user的code，服务端才能解析到用户昵称头像信息
        // this.getAuthCode().then((authCode) => {
        //   params.code = authCode
        // }).catch(err => {
        //   console.error(err)
        //   showToast(err.errorMessage);
        // }).finally(() => {
        //   showLoading();
        //   authLoginMerge({ ...params }).then(() => {
        //     hideLoading();
        //     this.onLoginFinish();
        //   }).catch(err => {
        //     hideLoading();
        //     showModal('提示', err.toString());
        //   });
        // })
      },
      fail: (res) => {
        console.log(res);
      },
    });
  }
  onGetPhoneNumberError(e) {
    showModal("提示", "请点击手机号登录/注册");
  }
  getAuthCode() {
    return new Promise((resolve, reject) => {
      my.getAuthCode({
        scopes: "auth_user",
        success: (res) => {
          const authCode = res.authCode;
          // 在服务端获取用户信息
          resolve(authCode);
        },
        fail: (err) => {
          console.log("my.getAuthCode 调用失败", err);
          reject(err);
        },
      });
    });
  }
  setLoginType(t) {
    if (!this.state.privacyAgreeStatus && this.state.isWeapp) {
      this.setState({
        showPrivacyPop: true,
      });
      return;
    }
    this.setState({
      loginType: t,
    });
  }
  // 处理用户隐私协议事件回调
  handleAgreePrivacyAuthorize(status) {
    this.setState({
      privacyAgreeStatus: status,
      showPrivacyPop: false,
    });
    if (status) {
      // 防止重复获取session
      let status = !this.hasRequestAuth;
      if (status) {
        this.hasRequestAuth = true;
      }
      requestAuth(status)
        .then(() => {
          this.onLoginFinish();
        })
        .catch((err) => {
          console.error(err);
        });
    }
    !status && Taro.exitMiniProgram();
  }
  closePop() {
    markAlertMessagesRead().finally(() => {
      Taro.navigateBack();
    });
    this.setState({
      showAdPop: false,
    });
    // Taro.showTabBar()
  }
  // 打卡新页面
  toPage(url: string, item?: any) {
    // 跳转小程序页面
    if (item && item.typeflag == 11) {
      let _url = /^\/.*/.test(url) ? url : `/${url}`; // 路径斜杠开头，兼容管理端配置时出错
      Taro.navigateTo({
        url: _url,
      });
      return;
    }
    markAlertMessagesRead().finally(() => {
      pushWebView(url, {
        replace: true,
      });
    });
  }
  render() {
    let {
      isNewUser,
      isWeapp,
      isAlipay,
      showPrivacyPop,
      userInfo,
      popupInfo,
      showAdPop,
    } = this.state;

    const jglhViewsForNormalLogin = (
      <View>
        <View className="content">
          <View className="item-cell item-input phone">
            <View className="item-cell__bd">
              <Input
                onInput={this.onFormInput.bind(this, "phone")}
                className="input tel"
                type="number"
                placeholder="请输入手机号"
              />
            </View>
          </View>
          {isNewUser && (
            <View className="item-cell item-input password">
              <View className="item-cell__bd">
                <Input
                  className="input"
                  type="text"
                  password
                  placeholder="请输入密码"
                  onInput={this.onFormInput.bind(this, "passWord")}
                />
              </View>
            </View>
          )}

          <View className="item-cell item-input security">
            <View className="item-cell__bd">
              <Input
                className="input"
                type="text"
                placeholder="请输入短信验证码"
                onInput={this.onFormInput.bind(this, "vcode")}
              />
            </View>
            {this.state.seconds <= 0 ? (
              <View className="link vcode" onClick={this.getVerify.bind(this)}>
                获取验证码
              </View>
            ) : (
              <View className="link vcode disabled">
                {this.state.seconds}秒
              </View>
            )}
          </View>
          <View className="tip">
            温馨提示：未注册交广领航的手机号，登录时将自动注册，且代表您已同意
            <Text className="tip-alt" onClick={this.toAgreement}>
              《用户注册协议》
            </Text>
          </View>
        </View>
        {!userInfo && isWeapp && (
          <Button
            className="btn"
            onClick={this.onGetUserProfile.bind(this, LoginType.Normal)}
          >
            登录
          </Button>
        )}
        {(userInfo || isAlipay) && (
          <Button className="btn" onClick={this.onNormalLogin.bind(this)}>
            登录
          </Button>
        )}
        <View className="link-areas">
          <Text
            className="link"
            onClick={this.setLoginType.bind(this, LoginType.Quick)}
          >
            {`${isAlipay ? "支付宝" : "微信"}`}用户快捷登录
          </Text>
        </View>
      </View>
    );

    const weappQuickLogin = (
      <View className="page-center">
        <View className="logo" />
        <Button
          className="btn"
          open-type="getPhoneNumber"
          onGetPhoneNumber={this.onGetPhoneNumber.bind(this)}
        >
          手机号快捷登录
        </Button>
        <View className="link-areas">
          <Text
            className="link"
            onClick={this.setLoginType.bind(this, LoginType.Normal)}
          >
            手机号登录/注册
          </Text>
        </View>
      </View>
    );

    const alipayQuickLogin = (
      <View className="page-center">
        <View className="logo" />
        {/* <View className="btn" onClick={this.onGetPhoneNumberFunc.bind(this)}>支付宝登录</View> */}
        <Button
          openType="getAuthorize"
          // openType="getPhoneNumber"
          scope="phoneNumber"
          onGetAuthorize={this.onGetPhoneNumber.bind(this)}
          // onGetPhoneNumber={this.onGetPhoneNumber.bind(this)}
          onError={this.onGetPhoneNumberError.bind(this)}
          type="primary"
          className="btn"
        >
          支付宝登录
        </Button>
        <View className="link-areas">
          <Text
            className="link"
            onClick={this.setLoginType.bind(this, LoginType.Normal)}
          >
            手机号登录/注册
          </Text>
        </View>
      </View>
    );

    const cheZhuKaViews = (
      <View>
        <Button
          className="btn-chezhuka-login"
          onClick={this.onGetUserProfile.bind(this, LoginType.CheZhuKa)}
        >
          自驾车主卡一键登录
        </Button>
      </View>
    );

    const loginType = this.state.loginType;
    return (
      <View>
        {loginType === LoginType.CheZhuKa && cheZhuKaViews}
        {loginType === LoginType.Normal && jglhViewsForNormalLogin}
        {loginType === LoginType.Quick && isWeapp && weappQuickLogin}
        {loginType === LoginType.Quick && isAlipay && alipayQuickLogin}
        {isWeapp && (
          <PrivacyPopup
            authorize={this.handleAgreePrivacyAuthorize.bind(this)}
            showPop={showPrivacyPop}
          />
        )}
        {popupInfo && showAdPop && (
          <AdPopup
            toPage={this.toPage.bind(this)}
            frameAd={popupInfo}
            close={this.closePop.bind(this)}
          />
        )}
      </View>
    );
  }
}
