@import './style/var.less';
// -----------------------------------
// power-scroll-pull
@power-scroll-pull-head-height: 100px;
@power-scroll-pull-head-font-size: @font-size-md;
@power-scroll-pull-head-text-color: @gray-6;
@power-scroll-pull-loading-icon-size: 32px;
// power-scroll
@power-scroll-list-text-color: @gray-6;
@power-scroll-list-text-font-size: @font-size-md;
@power-scroll-list-text-line-height: 100px;
@power-scroll-list-loading-icon-size: 32px;

.van-power-scroll-view {
  // overflow: hidden;
  user-select: none;
  // height: 100%;
    transform: rotate(180deg);
    direction: rtl;

  &__track {
    position: relative;
    min-height: 100%;
    transition-property: transform;
  }

  &__head {
    transform: rotate(180deg);
    direction: ltr;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 0;
    width: 100%;
    overflow: hidden;
    // text-align: center;
    transform: translateY(-100%);
    .theme(height, '@power-scroll-pull-head-height');
    .theme(color, '@power-scroll-pull-head-text-color');
    .theme(font-size, '@power-scroll-pull-head-font-size');
    // .theme(line-height,'@power-scroll-pull-head-height');
  }

  &__loading {
    .van-loading__spinner {
      .theme(width, '@power-scroll-pull-loading-icon-size');
      .theme(height, '@power-scroll-pull-loading-icon-size');
    }
  }

  // ---power-scroll
  &__loading,
  &__finished-text,
  &__error-text {
    transform: rotate(180deg);
    direction: ltr;
    display: flex;
    justify-content: center;
    align-items: center;
    // text-align: center;
    .theme(color, '@power-scroll-list-text-color');
    .theme(font-size, '@power-scroll-list-text-font-size');
    .theme(height,'@power-scroll-list-text-line-height');
  }

  &__placeholder {
    height: 0;
    pointer-events: none;
  }

  &__loading-icon {
    .van-loading__spinner {
      .theme(width, '@power-scroll-list-loading-icon-size');
      .theme(height, '@power-scroll-list-loading-icon-size');
    }
  }
}
