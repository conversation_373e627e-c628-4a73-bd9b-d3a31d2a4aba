import Taro from "@tarojs/taro";
import { parseURL, deleteURLSearchParams } from "./url";
/**
 * 跳转webview
 * @param url
 */
interface pushWebViewOptions {
  // 是否需要登录
  requireSignIn?: boolean;
  // 是否替换路由
  replace?: boolean;
  // 是否允许分享
  share?: boolean;
  // 页面标题
  title?: string;
}

/**
 * 打开webview
 * @param url 要打开的地址
 * @param options 参数
 */
export function pushWebView(
  url: string,
  config: pushWebViewOptions = {}
): void {
  let url2 = url;
  const options = Object.assign(
    {
      share: true,
    },
    config
  );
  // 移除lh_wvc参数
  // TODO: 解析lh_wvc参数
  try {
    var urlObj = parseURL(url);
    // console.log(urlObj)
    // app打开小程序页面目录
    const APP_TO_MP_PATH = "/actions/app/wxmp/app2mp.html";
    if (urlObj.pathname === APP_TO_MP_PATH) {
      const params = urlObj.params;
      Taro.navigateToMiniProgram({
        appId: params.appid,
        path: params.path,
        envVersion: params.env || "release", // develop|trial|release
        success: function (res) {
          console.log(res);
        },
        fail: (err) => {
          console.error(err);
          // showModal('提示', err.errMsg);
        },
      });
      return;
    }
    url2 = deleteURLSearchParams(url, ["lh_wvc"]);
  } catch (err) {
    console.error(err);
  }
  const requireSignIn = !!options.requireSignIn ? 1 : 0;
  const params = [
    `url=${encodeURIComponent(url2)}`,
    requireSignIn ? `requireSignIn=${requireSignIn}` : "",
    options.title ? `title=${options.title}` : "",
    options.share ? `share=1` : "share=0",
  ]
    .filter((value) => !!value)
    .join("&");
  const path = `/pages/web/index?${params}`;
  if (options.replace) {
    Taro.redirectTo({
      url: path,
    });
  } else {
    Taro.navigateTo({
      url: path,
    });
  }
}

/**
 * 跳转到登录页面
 * @param backURL
 */
export function login(backURL?: string) {
  Taro.navigateTo({
    url: `/pages/login/index?backurl=${backURL}`,
  });
}

export function showToast(title) {
  return Taro.showToast({
    title: String(title),
    icon: "none",
  });
}

export function showLoading(title = "加载中...") {
  // debugger
  return Taro.showLoading({
    title: title,
    mask: true,
  });
}

export function hideLoading() {
  return Taro.hideLoading();
}

export function showModal(
  title: string = "提示",
  msg: string,
  options: Object = {}
) {
  return Taro.showModal({
    confirmColor: "#EF7031",
    ...options,
    title: title,
    content: msg,
    // showCancel: false
  });
}
