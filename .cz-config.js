module.exports = {
  // type 可选列表
  types: [
    { value: "feat", name: "feat:     新增功能" },
    { value: "fix", name: "fix:      修复 bug" },
    { value: "docs", name: "docs:     文档变更" },
    { value: "style", name: "style:    代码格式(不影响功能，例如空格、分号等格式修正)" },
    { value: "refactor", name: "refactor: 代码重构(不包括 bug 修复、功能新增)" },
    { value: "perf", name: "perf:     性能优化" },
    { value: "test",  name: "test:     Adding missing tests" },
    { value: "chore", name: "chore:    其他修改, 比如构建流程, 依赖管理、版本好修正." },
    { value: "revert", name: "revert:   回退到一个commit" },
  ],
  // scope 可选列表
  scopes: [
    {name: 'components'},
    {name: 'utils'},
    {name: 'styles'},
    {name: 'deps'}, // 项目依赖
    {name: 'auth'},
    {name: 'other'}, // 其他修改
  ],
  allowCustomScopes: true, // 有bug，无法输入自定义scope https://github.com/leoforfree/cz-customizable/issues/201
  // 覆盖交互提示信息
  messages: {
    type: "请选择提交类型(必填)",
    scope: '请输入修改范围 scope(可选):',
    customScope: '请输入自定义的 scope:',
    subject: '简短描述本次修改:\n',
    body: '提供关于本次修改更具体的信息(可选),使用 "|" 换行:\n',
    breaking: '列举非兼容性重大的变更(可选):\n',
    footer: '列举出所有变更的 ISSUES CLOSED(可选)。 例如: #31, #34:\n',
    confirmCommit: '确定提交信息?',
  },
  // skip any questions you want
  skipQuestions: ['body', 'footer'],
  allowBreakingChanges: ['feat', 'fix'],
  // limit subject length
  subjectLimit: 100,
};
