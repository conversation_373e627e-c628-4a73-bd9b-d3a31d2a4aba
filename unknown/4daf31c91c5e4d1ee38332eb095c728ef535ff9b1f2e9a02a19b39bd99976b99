// import Taro from '@tarojs/taro';
import API from "../api";
import { doGet, doPost, doPostJson } from "../request";
import { getUserInfo } from "@/gdata";

// 获取首页信息
API.extend({
  "/traffic/messages": "/Radio/traffic/messages", // 路况信息请求接口
  "/traffic/report": "/Radio/traffic/envelope/message/v2", // 上报路况
  "/traffic/sidebar/items": "/Radio/traffic/map/right/search/items", // 侧边栏按钮列表
  "/traffic/violation/list": "/Radio/traffic/wf/record/list/forapp", // 违章列表
  "/traffic/charging/stations": "/Radio/api/chargingstations/nearby", // 充电站列表
  // 'qiniu/token': '/Radio/resource/cloud/bulk/gettoken?v=420', // 获取图片七牛上传token
  // 'qiniu/audio/token': '/Radio/resource/cloud/bulk/gettoken?type=0&v=420', // 获取音频七牛上传token
});

/**
 * @description: 路况信息请求接口
 * @param {Object} params - 包含以下参数的对象
 * @param {String} params.uid - 用户id
 * @param {number} params.x - 经度
 * @param {number} params.y - 纬度
 * @param {number} [params.axis=2] - 固定值 2，表示坐标系
 * @param {number} [params.scale=3] - 请求的范围，单位为千米，例如3km
 * @param {number} [params.v=460] - 版本号，默认为460
 * @param {number} [params.t] - 最后一条消息的时间戳
 * @param {String} [params.sid='HN0001'] - 电台id，默认为 HN0001
 *
 * @return {Promise} - 返回一个Promise对象，包含交通消息的结果
 */
let timestamp = new Date().getTime();
export const getTrafficMsgs = (params) => {
  let defaultParams = {
    axis: 2,
    scale: 3,
    t: 0,
    sid: "HN0001",
  };
  return doGet(API.dget("/traffic/messages"), {
    ...defaultParams,
    ...params,
  }).then((res) => {
    timestamp = new Date().getTime();
    return res;
  });
};

export function reportTraffic(params) {
  let userInfo = getUserInfo();
  let defaultParams = {
    axis: 0,
    expired: 0,
    roadid: null,
    ptype: "TRAFFIC", // 语音、文字路况时传空，图片路况时传TRAFFIC
    sid: "HN0001",
    uid: userInfo?.uid || "",
  };

  return doPost(API.dget("/traffic/report"), { ...defaultParams, ...params });
}

/**
 * @description: 获取侧边栏按钮列表
 * @param {Object} params - 包含以下参数的对象
 * @param {String} [params.sid='HN0001'] - 站点ID，默认为 HN0001
 * @return {Promise} - 返回一个Promise对象，包含侧边栏按钮列表
 */
export const getSidebarItems = (params = {}) => {
  let defaultParams = {
    sid: "HN0001",
  };
  return doGet(API.dget("/traffic/sidebar/items"), {
    ...defaultParams,
    ...params,
  });
};

/**
 * @description: 获取违章列表
 * @param {Object} params - 包含以下参数的对象
 * @param {number} params.lat - 纬度坐标
 * @param {number} params.lng - 经度坐标
 * @param {String} [params.v='480'] - 版本号，默认为 480
 * @param {String} [params.sid='HN0001'] - 站点ID，默认为 HN0001
 * @return {Promise} - 返回一个Promise对象，包含违章列表数据
 */
export const getViolationList = (params) => {
  let defaultParams = {
    v: "480",
    sid: "HN0001",
  };
  return doGet(API.dget("/traffic/violation/list"), {
    ...defaultParams,
    ...params,
  });
};

/**
 * @description: 获取附近充电站列表
 * @param {Object} params - 包含以下参数的对象
 * @param {number} params.latitude - 纬度坐标
 * @param {number} params.longitude - 经度坐标
 * @param {number} [params.distance=5] - 搜索半径，单位为公里，默认为5公里
 * @return {Promise} - 返回一个Promise对象，包含充电站列表数据
 */
export const getChargingStations = (params) => {
  let defaultParams = {
    distance: 5,
  };
  return doGet(API.dget("/traffic/charging/stations"), {
    ...defaultParams,
    ...params,
  });
};
