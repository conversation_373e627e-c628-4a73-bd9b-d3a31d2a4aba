import Taro, {
  getEnv,
  useRouter,
  useShareAppMessage,
  useShareTimeline,
} from "@tarojs/taro";
import { Video, View, Block } from "@tarojs/components";
import React, { useCallback, useEffect, useState } from "react";
// webSocket
import { getLiveRoomList } from "@/api/modules/chat";
import LiveVideo from "./components/LiveVideo/live.video";
import LiveMsgList from "./components/LiveMessage/messages";
import { pushWebView, getImageURL } from "@/tools";
import { liveModeType } from "@/enums";

export default () => {
  const env = getEnv();
  const router = useRouter();

  const [rooms, setRooms] = useState<Array<APP.LiveRoom>>([]);
  const [liveRoom, setLiveRoom] = useState<APP.LiveRoom>();
  const [onlineText, setOnlineText] = useState("");
  const [liveModeChangeMsg, setLiveModeChangeMsg] = useState<any>(null);
  useShareAppMessage((res) => {
    const shareInfo: any = {
      title: liveRoom?.shareTitle || "FM1041河南交通广播",
      path: `/pages/live/live?roomid=${liveRoom?.id || 1}`,
    };
    const promise = new Promise((resolve) => {
      setTimeout(() => {
        if (liveRoom) {
          if (liveRoom.shareTitle) {
            shareInfo.title = liveRoom.shareTitle;
          }
          if (liveRoom.shareImage) {
            shareInfo.imageUrl = getImageURL(liveRoom.shareImage);
          }
        }
        resolve(shareInfo);
      }, 600);
    });
    return {
      title: liveRoom?.shareTitle || "FM1041河南交通广播",
      path: `/pages/live/live?roomid=${liveRoom?.id || 1}`,
      promise,
    };
  });
  useShareTimeline(() => {
    const shareInfo: any = {
      title: liveRoom?.shareTitle || "FM1041河南交通广播",
      query: `roomid=${liveRoom?.id || 1}`,
    };
    if (liveRoom?.shareImage) {
      shareInfo.imageUrl = getImageURL(liveRoom?.shareImage);
    }
    return shareInfo;
  });
  // 初始化
  useEffect(() => {
    const { roomid } = router.params;
    getLiveRoomList()
      .then((res) => {
        // console.log(res);
        if (res.length) {
          setRooms(res);
          if (roomid) {
            let index = res.findIndex((item) => {
              return item.id == roomid;
            });
            if (index > -1) {
              setLiveRoom(res[index]);
              return;
            }
            setLiveRoom(res[0]);
          } else {
            setLiveRoom(res[0]);
          }
        } else {
          setRooms([]);
          setLiveRoom(undefined);
        }
      })
      .catch((err) => {
        Taro.showToast(err.msg);
      });
  }, [router.params]);

  const onSwitchRoom = useCallback((room: APP.LiveRoom) => {
    // 报错直接返回上一级完事
    setLiveRoom(room);
    setLiveModeChangeMsg(null);
  }, []);

  const onlineCountChange = useCallback((value: string) => {
    setOnlineText(value);
  }, []);

  // 2024-07-09 营销活动需求：改变直播模式，通过websocket下发消息
  // 消息体中liveMode字段：1-正常，-1-直播结束，直播结束需要停止直播源播放，并跳转到直播数据页面
  const onLiveModeChange = useCallback((data: APP.Message) => {
    setLiveModeChangeMsg(data);
  }, []);

  return (
    <React.StrictMode>
      <View className="page-wrap">
        {/* <View className="fixed-box">这是一个固定相</View> */}
        <Block>
          <LiveVideo
            env={env}
            rooms={rooms}
            liveRoom={liveRoom}
            onSwitchRoom={onSwitchRoom}
            onlineText={onlineText}
            liveModeChangeMsg={liveModeChangeMsg}
            // onSeekComplete={onSeekComplete}
          />
        </Block>
        <Block>
          <LiveMsgList
            room={liveRoom}
            onlineCountChange={onlineCountChange}
            onLiveModeChange={onLiveModeChange}
          />
        </Block>
      </View>
    </React.StrictMode>
  );
};
