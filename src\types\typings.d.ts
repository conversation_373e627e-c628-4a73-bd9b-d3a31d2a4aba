declare namespace APP {
  export type Resp<T = any> = {
    data: T;
    success: true;
  };

  export type Action<T = any> = {
    code: number;
    data: T;
    msg: string;
    success: boolean;
  };

  export type ImageResp = {
    url: string;
  };
  // 0文本 1语音 2图片 3打赏 4小视频 5关键词回复 6用户发送消息的回执 7系统通知消息 8用户关进小黑屋的通知消息 9连接成功后给用户返回的消息 10未读私信消息总条数
  export type MessageType = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;
  // 1私信消息 2聊天室群消息 3系统通知消息 99心跳包
  export type NoticeType = 1 | 2 | 5 | 99;

  export type NavigatorContent = {
    content: string;
    title: string;
    url: string;
  };

  export type LiveRoom = {
    id: number;
    sid: string;
    name: string;
    status: number;
    createTime: number;
    updateTime: number;
    creator: string;
    updator: string;
    radioLabel: string;
    radioLogo: string;
    livePlayUrl: string;
    liveVideoUrl: string;
    shareTitle: string;
    shareImage: string;
    currentProgramInfo: any;
    videoAdsUrl: any;
    roomId: string;
  };

  export type Message<T = string> = {
    id: string;
    content: T;
    type: MessageType;
    room: string;
    fromId: string;
    to?: any;
    t?: number;
    receiverId?: any;
    status: number;
    programId: string;
    sid: string;
    sender: {
      uid: string;
      name: string;
      gender: any;
      level: any;
      phone: string;
      score: any;
      label: any;
      distance: any;
      status: number;
      email: any;
      type: number;
      portrait: string;
      url: any;
      intro: any;
      image: any;
      t: number;
      programId: any;
      sid: string;
      vipStatus: true;
    };
    receiver: any;
    show: any;
    mtype: NoticeType;
    sendId: any;
    rewardGiftResource: any;
    lhxAmount: any;
    rewardGiftSuccessToSocketData: any;
    rewardGiftData: any;
    randomNumber?: number;
    replyContent?: string;
    replyContentJson?: string;
    exactMatchKeywords: number;
    sending?: string;
    msgId?: string;
    toId?: string;
    speech: string | number;
    onlineCount?: number;
    liveMode?: number;
    liveOfflineUrl?: string;
  };
}
