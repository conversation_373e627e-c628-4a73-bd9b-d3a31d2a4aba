import Taro from "@tarojs/taro";
import React, { Component } from "react";
import { View, Image, Text } from "@tarojs/components";
import { Icon, Popup } from "@antmjs/vantui";
import { pushWebView } from "@/tools";
import { getImageURL } from "@/util";
import "./index.scss";

interface Props {
  showPop: boolean;
  address: string;
  onReport: Function;
  onClose: Function;
  onNavigation: Function;
}

interface State {
  // playStatus: boolean;
}

export default class Index extends Component<Props, State> {
  constructor(props) {
    super(props);

    this.state = {
      // playStatus: false,
    };
  }

  componentDidMount() {}

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  handleReport() {
    this.props.onReport();
  }

  handleReportPopupClose() {
    this.props.onClose();
  }

  handleNavigation() {
    this.props.onNavigation();
  }

  render() {
    let { showPop, address } = this.props;

    return (
      <Popup
        className="report-popup"
        show={showPop}
        position="bottom"
        zIndex={2025}
        closeable
        overlayStyle={{ backgroundColor: "rgba(0, 0, 0, 0)" }}
        onClose={this.handleReportPopupClose.bind(this)}
      >
        {address && (
          <View className="address">
            <Icon name="location-o" size="28px" className="icon" />
            <Text className="location-address">{address}</Text>
          </View>
        )}
        <View className="popup-btns">
          <View
            className="popup-btn cancel"
            onClick={this.handleNavigation.bind(this)}
          >
            去这里
          </View>
          <View className="popup-btn" onClick={this.handleReport.bind(this)}>
            路况上报
          </View>
        </View>
      </Popup>
    );
  }
}
