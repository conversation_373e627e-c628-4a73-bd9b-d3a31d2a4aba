import urlParse from 'url-parse';
import qs from 'query-string';

export function getURLSearchParams(url: string) {
  var urlObj = urlParse(url);
  var querys = qs.parse(urlObj.query);
  return querys;
}

export function parseURL(url: string) {
  var urlObj = urlParse(url);
  var params = qs.parse(urlObj.query);
  urlObj.params = params;
  return urlObj;
}

export function deleteURLSearchParams(url: string, params: Array<string>) {
  var urlObj = parseURL(url);
  var querys = urlObj.params;
  params.forEach(key => {
    delete querys[key];
  })
  var querys2 = qs.stringify(querys)
  urlObj.query = querys2;
  const url2 = urlObj.toString();
  return url2;
}

export function addURLSearchParams(url: string, params: Object) : string {
  var urlObj = urlParse(url);
  var querys = qs.parse(urlObj.query);
  Object.keys(params).forEach(key => {
    querys[key] = params[key];
  })
  var querys2 = qs.stringify(querys)
  urlObj.query = querys2;
  const url2 = urlObj.toString();
  return url2;
}
