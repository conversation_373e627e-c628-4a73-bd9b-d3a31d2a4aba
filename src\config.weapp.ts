export default {
  pages: [
    "pages/index/index",
    "pages/index/landing",
    "pages/live/live",
    "pages/shop/index",
    "pages/shop/shop",
    "pages/user/index",
    "pages/web/index",
    "pages/order/index",
    "pages/order/detail",
    "pages/login/index",
    // 'pages/web/web',
    "pages/payment/index",
    "pages/wxlive/index",
    "pages/launchwp/index",
    "pages/platform/order",
    "pages/map/index",
    "pages/markers/index",
    "pages/traffic-voice/index",
    "pages/traffic-text-image/index",
    "pages/lottery/index",
    "pages/lottery-result/index",
  ],
  window: {
    backgroundTextStyle: "light",
    // navigationBarBackgroundColor: '#FF5925',
    navigationBarBackgroundColor: "#fff",
    navigationBarTitleText: "交广领航",
    navigationBarTextStyle: "black",
    navigationStyle: "default",
  },
  tabBar: {
    custom: false,
    selectedColor: "#000",
    borderStyle: "white",
    list: [
      {
        pagePath: "pages/index/index",
        text: "首页",
        iconPath: "./images/v2/icon-home.png",
        selectedIconPath: "./images/v2/icon-home-active.png",
      },
      {
        pagePath: "pages/shop/index",
        text: "商城",
        iconPath: "./images/v2/icon-mall.png",
        selectedIconPath: "./images/v2/icon-mall-active.png",
      },
      {
        pagePath: "pages/live/live",
        text: "互动",
        iconPath: "./images/v2/live.png",
        selectedIconPath: "./images/v2/live-active.png",
      },
      {
        pagePath: "pages/user/index",
        text: "我的",
        iconPath: "./images/v2/icon-mine.png",
        selectedIconPath: "./images/v2/icon-mine-active.png",
      },
    ],
  },
  plugins: {
    "live-player-plugin": {
      version: "1.3.5", // 注意填写该直播组件最新版本号，微信开发者工具调试时可获取最新版本号（复制时请去掉注释）
      provider: "wx2b03c6e691cd7370", // 必须填该直播组件appid，该示例值即为直播组件appid（复制时请去掉注释）
    },
  },
  requiredPrivateInfos: ["getLocation"],
  permission: {
    "scope.userLocation": {
      desc: "天气显示自动切换地址",
    },
  },
  requiredBackgroundModes: ["audio"],
  __usePrivacyCheck__: true,
};
