import Taro from "@tarojs/taro";
import React, { Component } from "react";
import { View, Image, Swiper, SwiperItem } from "@tarojs/components";
import { getFullImagePath, pushWebView } from "@/tools";

import "./index.scss";

interface PicLoop {
  id: number;
  image: string;
  url: string;
}

interface Props {
  picLoop: PicLoop[];
}

interface State {
  current: number;
  swiperHeight: number;
}

export default class Index extends Component<Props, State> {
  constructor(props) {
    super(props);

    this.state = {
      current: 0,
      swiperHeight: 0,
    };
  }

  componentDidMount() {
    Taro.createSelectorQuery()
      .select(".sub-swiper")
      .boundingClientRect((rect) => {
        if (rect && !Array.isArray(rect)) {
          this.setState({ swiperHeight: rect.width });
        }
      })
      .exec();
  }

  toPage(url: string, item?: any) {
    if (!url) return;
    if (item && item.typeflag == 11) {
      let _url = /^\/.*/.test(url) ? url : `/${url}`;
      Taro.navigateTo({
        url: _url,
      });
      return;
    }
    pushWebView(url);
  }

  changeEnd(ev) {
    this.setState({
      current: ev.currentTarget.current,
    });
  }

  render() {
    let { picLoop } = this.props;
    let { swiperHeight } = this.state;

    return (
      <View className="sub-banner">
        <Swiper
          className="sub-swiper"
          style={{ height: `${swiperHeight}px` }}
          circular
          onChange={this.changeEnd.bind(this)}
        >
          {picLoop.map((item) => {
            return (
              <SwiperItem
                key={item.id}
                onClick={this.toPage.bind(this, item.url)}
              >
                <Image
                  showMenuByLongpress
                  className="img"
                  mode="widthFix"
                  src={getFullImagePath(item.image)}
                />
              </SwiperItem>
            );
          })}
        </Swiper>
        <View className="dots">
          {picLoop.length > 1 &&
            picLoop.map((item, index) => {
              return (
                <View
                  key={item.id}
                  className={[
                    "dot",
                    index === this.state.current ? "this" : null,
                  ].join()}
                />
              );
            })}
        </View>
      </View>
    );
  }
}
