import Taro from "@tarojs/taro";
import API from "../api";
import { doGet, doPost } from "../request";

const isAlipay = Taro.getEnv() === Taro.ENV_TYPE.ALIPAY;
// 获取首页信息
API.extend({
  "/home/<USER>/data": "/Radio/base/infos", // 获取首页信息
  "/home/<USER>/list": "/Radio/actions/list", // 获取活动列表
  "home/config": "/fe/data/weapp_config.json", // 获取小程序首页个性配置信息
  "home/ads": "/Radio/advertisement/infos", // 获取小程序首页弹窗广告信息
  "alert/messages": "/Radio/alert/messages/get", // 登录后针对个人用户的提醒弹窗信息
  "alert/messages/read": "/Radio/alert/messages/read", // 标记已读-登录后提醒弹窗
  "topic/setting/info": "/Radio/base/topic/setting/info", // 百变专题
});

export function getActionList() {
  let data = {
    type: 2,
    orient: 1,
    max: 15,
    sort: 0.0,
  };

  return doGet(API.dget("/home/<USER>/list"), data);
}

export function getHomeData() {
  const platform = isAlipay ? "ALIPAY_XCX" : "XCX";

  const params = {
    platform,
  };
  return doGet(API.dget("/home/<USER>/data"), params);
}

export function getHomeAdData() {
  return doGet(API.dget("home/ads"));
}

export function getAlertMessages() {
  return doGet(API.dget("alert/messages"));
}

export function markAlertMessagesRead() {
  return doPost(API.dget("alert/messages/read"));
}

export function getTopicData() {
  const params = {
    sid: "HN0001",
  };
  return doGet(API.dget("topic/setting/info"), params);
}

interface WeAppHomeConfig {
  buttonWhiteList: Array<String>;
  buttonList: [];
  actionBlackList: Array<String>;
}

export function getWeappHomeConfig(): Promise<{
  buttonWhiteList: Array<String>;
  buttonList: [];
  actionBlackList: Array<String>;
}> {
  return doGet(API.dget("home/config"), { t: Date.now() });
}

export const ret = {};
