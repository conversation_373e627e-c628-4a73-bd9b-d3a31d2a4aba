import Taro, { getCurrentInstance } from '@tarojs/taro'
import React, { Component }  from 'react'
import { WebView, View } from '@tarojs/components'
import { getWebViewURL, showToast, showModal, getURLSearchParams, getFullImagePath } from '@/tools'
import { getLoggedIn, setLoggedIn, getLoginCount, getWebViewCallbackURL, setWebViewCallbackURL } from '@/gdata';

interface Props { }

interface State {
  pageURL: string,
  originURL: string,
  loggedIn:  boolean,
  loginCount:  number,
  data?: any,
}

const EMPTY_PAGE = 'about:blank';

export default class Index extends Component<Props, State> {
  $instance: any = getCurrentInstance()
  constructor(props) {
    super(props);
    this.state = {
      pageURL: EMPTY_PAGE,
      originURL: EMPTY_PAGE,
      loggedIn: getLoggedIn(),
      loginCount: getLoginCount(),
      data: {},
    };
  }

  componentWillMount () {
    // 初始化分享参数
    let url = `${BASE_URL}/actions/app/mcar/?env=weixin#/account/orders`;

    const webviewURL = getWebViewURL(url);
    this.setState({
      pageURL: webviewURL,
      originURL: getWebViewURL(url, false), // 原始地址，不带授权参数
    });
    // 页面需要登录且当前未登录时，跳转到登录页面去
    // console.log(this.$instance.router)
    // TODO: 跳转指定backurl
    const isLoggedIn = getLoggedIn();
    console.log('isLoggedIn:', isLoggedIn)
    const requireSignIn = !isLoggedIn;
    if (requireSignIn) {
      console.log('nav to login')
      Taro.navigateTo({
        url: '/pages/login/index',
      })
    }
  }

  componentDidMount () { }

  componentWillUnmount () { }

  // config: Config = {
  //   navigationBarTitleText: '交广领航',
  // }

  componentDidShow () {
    const isLoggedIn = getLoggedIn();
    const loginCount = getLoginCount();
    setLoggedIn(isLoggedIn);
    const callbackURL = getWebViewCallbackURL();
    // 刷新页面条件：
    // 1. 登录状态发生变化
    // 2. 登录状态可能发生变化（跳转到了登录页面后返回）
    // 3. 回调url参数不为空（收银台会设置callbackURL）
    const loginStateChanged = !this.state.loggedIn && isLoggedIn;
    const shouldRefreshPage = this.state.loginCount != loginCount || loginStateChanged || ((this.state.loginCount != loginCount || loginStateChanged) && !!callbackURL);
    // console.log('state:', this.state, 'loggedIn:', isLoggedIn);
    // console.log('shouldRefreshPage:', shouldRefreshPage);
    // debugger
    if (shouldRefreshPage) {
      this.setState({
        loggedIn: isLoggedIn,
        loginCount: loginCount,
      });
      let url = getWebViewURL(this.state.originURL);
      if (callbackURL) {
        url = getWebViewURL(callbackURL);
        // 用完callbackURL，清除 callbackURL
        setWebViewCallbackURL('');
      }
      this.reloadPage(url)
    }
  }
  /**
   * 刷新页面
   * @param url
   */
  reloadPage(url) {
    // showToast('reloadPage...')
    // webview没有提供刷新方法，此处 先将url设为 empty_url，再设置webview的url可触发webview重新加载页面
    this.setState({
      pageURL: EMPTY_PAGE,
    });
    setTimeout(() => {
      this.setState({
        pageURL: url,
      });
    }, 300)
  }
  onShareAppMessage(res) {
    // console.log(this.$instance.router.params, '6');
    // let path = '/pages/web/index?url=' + encodeURIComponent(this.state.originURL);
    // webview页面的分享地址设为 webview页面
    // console.log(res);
    const url = res.webViewUrl || this.state.originURL;
    let path = '/pages/web/index?url=' + encodeURIComponent(url);
    const couldShare = this.$instance.router.params.share != 0;
    // console.log('couldShare,', couldShare)
    if (!couldShare) {
      path = '/pages/index/index';
    }
    // console.log(path)
    // showToast(path);
    const shareInfo = {
      path: path,
      title: '交广领航',
      imageUrl: null,
    };
    const promise = new Promise(resolve => {
      setTimeout(() => {
        const shareData = this.state.data;
        if (shareData) {
          if(shareData.title) {
            shareInfo.title = shareData.title;
          }
          if(shareData.imgUrl) {
            shareInfo.imageUrl = shareData.imgUrl;
          }
        }
        console.info('shareInfo:', shareInfo)
        resolve(shareInfo)
      }, 10)
    })
    // showModal('提示', JSON.stringify(shareInfo) + JSON.stringify(shareData))
    return {
      ...shareInfo,
      promise
    };
    // return { path: path };
  }

  componentDidHide () { }
  onWebViewError(e) {
    console.error(e);
  }
  onWebViewLoad(e) {
    console.info(e);
    const isLoggedIn = getLoggedIn();
    this.setState({
      loggedIn: isLoggedIn,
    });
  }
  onWebViewMessage(e) {
    let data = e.detail?.data;
    if (Array.isArray(e.detail.data)) {
      data = e.detail?.data?.slice(-1)[0];
    }
    this.setState({
      data: data,
    })
  }
  render () {
    // 频繁切换webview可能会导致小程序报错：同一个页面只能渲染一个webview
    return (
      /* this.state.pageURL === EMPTY_PAGE ?
      <View className='loading'>loading</View> : */
      <WebView
        onError={this.onWebViewError.bind(this)}
        onLoad={this.onWebViewLoad.bind(this)}
        onMessage={this.onWebViewMessage.bind(this)}
        src={this.state.pageURL}
      />
    )
  }
}
