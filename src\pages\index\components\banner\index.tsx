import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { View, Image } from '@tarojs/components'
import { getFullImagePath } from '../../../../tools'

import './index.scss'


interface Banner {
  articleTitle: string
  endTime: any
  id: number
  image: string
  permCode: number
  sid: string
  sort: number
  startTime: any
  title: string
  type: number
  url: string
}
interface Item {
  scale: number,
  zIndex: number,
  mv: number,
  width: number,
}
interface Props {
  toPage: Function
}

interface State {
  swiperIndex: number,
  swiperInfo: Item[],
  bannerList: Banner[]
}

export default class Index extends Component<Props, State> {
  private bannerView: any;
  private isFirst: boolean = true;
  private timer: any;
  private pW: number;

  // config: PageConfig = {
  //   navigationBarTitleText: 'banner??',
  // }

  constructor(props) {
    super(props);

    this.state = {
      swiperIndex: 0,
      swiperInfo: [],
      bannerList: []
    };
  }

  componentWillMount () {
  }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }
  toPage(url) {
    this.props.toPage(url);
  }

  setBannerList(list:Banner[]) {
    // ???
    // let f = list.slice(0, 3);
    // let b = list.slice(list.length - 3, 3);
    // list = [
    //   ...f,
    //   ...list,
    //   ...b,
    // ];
    this.setState({
      bannerList: list
    }, () => {
      console.log('banner', this.state.bannerList);
      this.updateView();
    });
  }

  changeEnd(ev) {

  }

  autoView() {
    clearInterval(this.timer);
    this.timer = setInterval(() => {
      let index = this.state.swiperIndex + 1;
      if (index >= this.state.swiperInfo.length) {
        index = 0;
      }
      this.translateItem(index);

    }, 5000);
  }

  translateItem(activeIndex) {
    let tmp:Item[] = [];
    let length = this.state.swiperInfo.length;
    let curIndex = activeIndex;
    let pre = curIndex > 0 ? curIndex - 1 : length - 1;
    let next = curIndex == length - 1 ? 0 : curIndex + 1;
    this.state.swiperInfo.forEach((item, index) => {
      let active = activeIndex === index;
      tmp.push({
        mv: curIndex === index ? item.width : pre === index ? 0 : next === index ? item.width * 2 : item.width * index,
        scale: active ? 1 : 0.8,
        zIndex: curIndex === index ? 3 : pre === index ? 1 : next === index ? 1 : 0,
        width: item.width,
      });
    })
    this.setState({
      swiperInfo: tmp,
      swiperIndex: activeIndex
    });
  }

  componentDidUpdate() {
  }

  updateView() {
    if (this.isFirst || true) {
      let obj = this.bannerView.boundingClientRect();
      let items = obj.selectAll('.img-c').boundingClientRect();
      items.exec((res) => {
        let list = res[1];
        let curIndex = 0;
        let pre = curIndex > 0 ? curIndex - 1 : list.length - 1;
        let next = curIndex == list.length - 1 ? 0 : curIndex + 1;
        let tmp:Item[] = [];

        list.forEach((item, index) => {
          this.pW = item.width;
          tmp.push({
            width: item.width,
            scale: curIndex === index ? 1 : 0.8,
            zIndex: curIndex === index ? 3 : pre === index ? 1 : next === index ? 1 : 0,
            mv: curIndex === index ? item.width : pre === index ? 0 : next === index ? item.width * 2 : 0,
          });
        })
        if (this.state.bannerList.length > 0) {
          this.isFirst = false;
        }
        this.setState({
          swiperInfo: tmp,
        }, () => {
          // this.autoView();
        });
      })
    }
  }


  render () {
    // let {bannerList} = this.props;
    let { swiperInfo, bannerList } = this.state;
    // console.log('1111111222');
    return (
      <View className="banner">
        <View className="swiper"
          ref={(res) => {
            this.bannerView = res;
          }}
        >
          {
            bannerList &&
            bannerList.map((item, index) => {
              let styleStr = `transform: translateX(${swiperInfo[index] ? swiperInfo[index].mv : 0}px) scale(${swiperInfo[index] ? swiperInfo[index].scale : 1})`;
              return <View className="item" key={item.id} style={styleStr} onClick={this.toPage.bind(this, item.url)}>
                        <Image className="img-c" src={getFullImagePath(item.image)} />
                      </View>
            })
          }
        </View>
      </View>
    )
  }
}
