import React from 'react'
import Taro from '@tarojs/taro'
import { isPhone } from "@/util";
import { Text, View } from "@tarojs/components";
import styles from '../index.module.less'

const Index: React.FC<{
  className?: string
  speech: string | number
}> = props => {
  const calculateWidth =  React.useMemo(() => {
    const speech = Number(props.speech) || 0
    const width = speech / 60 * 700 + 70
    if (width > 375) {
      return {
        width: "375rpx"
      }
    }

    return {
      width: width + "rpx"
    }
  }, [props.speech])
  return React.useMemo(() => {
    return <View className={`${styles.voiceWrap } ${props.className}`} style={calculateWidth}>
      <View className={`${styles.voice}`} />
      <Text>{`${props.speech}"`}</Text>
    </View>
  } ,[calculateWidth, props.className, props.speech])
}
export default Index
