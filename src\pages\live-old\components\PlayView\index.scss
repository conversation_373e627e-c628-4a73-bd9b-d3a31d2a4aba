.play-wrap {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 400px;

  .play-img {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
  }

  .myVideo {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 1;
    z-index: 5;
  }
  .tv {
    position:absolute;
    right:10rpx;
    bottom:10rpx;
    width: 55px;
    height: 55px;
    z-index: 4;
    background-image: url(../../images/vadio_zb_vedio.png);
    background-size: contain;
  }
  .fm {
    position: absolute;
    right: 10px;
    bottom: 2px;
    width: 55px;
    height: 55px;
    z-index: 4;
    background: url(../../images/vadio_zb_fm.png);
    background-size: contain;
  }

  .player-btn {
    width: 70px;
    height: 70px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -35px;
    margin-left: -35px;
    background: url(../../images/big_play_btn.png);
    background-size: 160px 70px;
    background-position: -7px center;
    z-index: 3;
    &.player-btn-play {
      background-position: 77px center;
    }
  }

  
  .wave {
    position: absolute;
    top:50%;
    left:50%;
    transform: translateY(-50%) translateX(-50%);
    width: 80%;
    height: 100px;
    z-index: 3;
    vertical-align: middle;
  }
  .rotate {
    position: absolute;
    top:50%;
    left:50%;
    margin-top:-30rpx;
    margin-left:-30rpx;        
    width: 60px;
    height: 60px;
    z-index: 3;
  }
}
