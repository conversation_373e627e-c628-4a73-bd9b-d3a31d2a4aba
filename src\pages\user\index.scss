@import "~taro-ui/dist/style/components/fab.scss";
page {
  background: #f2f4f5;
}
.user-info {
  display: flex;
  // margin-bottom: 10px;
  .user-name {
    font-weight: bold;
    font-size: 36px;
  }

  .user-image-avatar{
    position: relative;
    .user-vip {
      display: inline-block;
      width: 80rpx;
      height: 28rpx;
      background: url(./images/vipTitle.png);
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      vertical-align: middle;
      position: absolute;
      left: 0;
      bottom: 11px;
      margin-left: 20px;
    }

  }
  .user-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    // margin-right: 20rpx;
    background: rgb(238, 238, 238);
  }

  .user-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 24px;
    &.not-loggedin {
      // display: flex;
      // align-items: center;
      // flex-direction: column;
      // justify-content: left;
    }
    .user-gold {
      font-size: 30px;
      line-height: 60px;
      .rmb {
        color: #fff000;
        color: #FF5925;
        display: inline;
      }
    }
    .user-right{
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
.titleBox{
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333333;
  margin: 0 30px 30px 30px;
  padding-top: 30px;
  .title{
    font-size: 32px;
    font-weight: 800;
  }
  .rightTitle{
    font-size: 20px;
    font-weight: 500;
    background: url(./images/icon-right.png) no-repeat center right ;
    background-size: 24px;
    padding-right: 30px;

  }
}
//  用户头像部分
.userInfo {
  padding: 30px;
  padding-top: 20px;
  box-sizing: border-box;
  color: black;
  // margin-top: -4px;
  // background: url(./images/topImage.png) no-repeat;
  width: 100%;
  // background-size: 100%;
  overflow: hidden;
  position: relative;

  .sign {
    // position: absolute;
    // top: 17px;
    // right: 0;
    font-size: 24px;
    color: white;
    border-radius: 30px;
    width: 170px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    background: linear-gradient(90deg, #FE4439, #F85F2A);
    box-shadow: 0px 5px 0px 0px rgba(254, 70, 56, 0.1);
  }
  .btn-account {
    font-size: 24px;
    color: #666;
    background: url(./images/icon-right.png) no-repeat center right ;
    background-size: 24px;
    padding-right: 30px;
  }
  .userImg {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: #fff;
    float: left;
    margin-right: 20px;
    overflow: hidden;

    .img {
      width: 100%;
      height: 100%;
    }
  }

}

// 内容部分
.box{
  box-sizing: border-box;
  margin: 0 30px;
  padding-bottom: 30px;
  .spacing{
    margin-bottom: 30px;
  }
}
.blockBg{
  background: #ffffff;
  border-radius: 20px;
}

.tab-top{
  height: 194rpx;
  .tab-top-item{
    display: flex;
    align-items: center;
    .tab-item{
      padding: 30px 0;
    }
  }
}

.tabList {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  .tab-item{
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 33.********%;
    .tab-img{
      .img{
        width: 90rpx;
        height: 90rpx;
      }
    }
    .tab-label{
      font-size: 24rpx;
      font-weight: bold;
      margin-top: 22rpx;
    }
  }
}
.vipImageBox{
  height: 115px;
  .vipImage{
    width: 100%;
    height: 100%;
  }
}

.swiperBody{
  // 订单部分
  .myOrder{
    height: 155px;
    .swiperBox{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .menu-item{
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 0 0 20%;
        position: relative;
        padding-top: 20px;
        .menu-item-img{
          width: 44px;
          height: 44px;
          .img{
            width: 100%;
            height: 100%;
          }
        }
        .menu-item-text{
          font-size: 24px;
          font-weight: 500;
          color: #333333;
          margin-top: 23px;
        }
        .menu-item-badge{
          position: absolute;
          top: 6px;
          right: 20px;
          display: inline-block;
          background: #FD4925;
          width: 36px;
          height: 24px;
          color: #ffffff;
          font-size: 14px;
          text-align: center;
          border-radius: 18px;
          line-height: 24px;
        }
      }

    }
  }
  .myOrderAli{
    .swiperBox{
      .menu-item{
        padding-bottom: 20px;
      }
    }

  }
  // 指示点样式
  .spot-pagination {
    display: flex;
    justify-content: center;
    padding-bottom: 30px;
    .spot-pagination-bullet{
      margin-right: 10px;
      border-radius: 4px;
      height: 6px;
      width: 20px;
      background: #F3F3F3
    }
    // 当前指示点样式
    .spot-pagination-bullet-active {
      background: #FD4925;
    }
  }
}

.centerBox{
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin-top: 6px;
  .center-item{
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 0 0 25%;
    margin-bottom: 43px;
    .center-item-img{
      width: 52px;
      height: 52px;
      .img{
        width: 100%;
        height: 100%;
      }
    }
    .center-item-text{
      font-size: 24px;
      font-weight: 500;
      color: #333333;
      margin-top: 19px;
    }
    &:nth-last-child(-n+4){
      margin-bottom: 32px;
    }
  }
}

.vip-box{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .img-item{
    flex: 1;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    &:last-child{
      margin-left: 15px;
    }
    .vipImg{
      width: 100%;
      border-radius: 20px 20px 0 0;
    }
    .img-name{
      background: #fff;
      border-radius: 0 0 20px 20px;
      padding:10px 16px;
      font-size: 24px;
      color: #333;
    }
  }
}
.page-bottom {
  text-align:center;
  position: absolute;
  bottom: 20px;
  color: rgb(202, 202, 202);
  width: 100%;
}

.at-modal-content{
  display: flex;
  flex-direction: column;
  justify-content: center;
}
