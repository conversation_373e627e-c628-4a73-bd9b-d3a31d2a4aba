import Taro from "@tarojs/taro";
import { View, Image } from "@tarojs/components";
import { Popup } from "@antmjs/vantui";
import React, { ReactNode } from "react";
import { getImageURL } from "@/util";
import "./index.scss";

export interface PrizePopupProps {
  /** 是否显示弹窗 */
  show: boolean;
  /** 奖品图片URL或七牛key */
  prizeImage: string;
  /** 是否需要通过getImageURL处理图片地址 */
  useImageURL?: boolean;
  /** 是否显示圆角 */
  round?: boolean;
  /** 点击遮罩是否关闭弹窗 */
  closeOnClickOverlay?: boolean;
  /** 关闭弹窗回调 */
  onClose?: () => void;
  /** 图片点击事件回调 */
  onImageClick?: () => void;
  /** 额外的弹窗内容 */
  children?: ReactNode;
  /** 自定义类名 */
  className?: string;
}

const PrizePopup: React.FC<PrizePopupProps> = ({
  show,
  prizeImage,
  useImageURL = true,
  round = true,
  closeOnClickOverlay = false,
  onClose,
  onImageClick,
  children,
  className = "",
}) => {
  const handleClose = () => {
    onClose && onClose();
  };

  const handleImageClick = () => {
    onImageClick && onImageClick();
  };

  const imageSrc = useImageURL ? getImageURL(prizeImage) : prizeImage;

  return (
    <Popup
      show={show}
      round={round}
      closeable
      closeOnClickOverlay={closeOnClickOverlay}
      onClose={handleClose}
      className={`prize-popup-container ${className}`}
    >
      <View className="prize-popup">
        <View className="prize-image-wrapper" onClick={handleImageClick}>
          <Image src={imageSrc} className="prize-image" mode="aspectFit" />
        </View>
        {children}
      </View>
    </Popup>
  );
};

export default PrizePopup;
