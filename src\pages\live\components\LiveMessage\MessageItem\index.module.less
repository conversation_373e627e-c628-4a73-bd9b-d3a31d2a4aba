.notice{
  width: 100%;
  text-align: center;
  font-size: 24px;
  margin: 10px 0;
  .text {
    display: inline-block;
    max-width: 300px;
    background-color: #E1E1E1;
    padding: 5px 10px;
    border-radius: 10px;
    color: #4A4A4A;
  }
}
.messageItem {
  transform: rotate(180deg);
  // direction: ltr;
  flex-direction: row-reverse;
  margin: 15px 0;
  padding: 0 20px;
  flex-shrink: 0;
  display: flex;
  justify-content: left;
  box-sizing: border-box;
  &.right{
    flex-direction: row;
    justify-content: right;
    &>.body{
      // align-items: flex-end;
      align-items: flex-start;
      & > .content.text {
        background-color: #95EC69;
        color: #333333;
      }
    }
  }
  & > .messageAvatar {
    width: 70px;
    height: 70px;
    border-radius: 5px;
  }
  &>.body{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    direction: ltr;
    // align-items: flex-end;
    &>.name {
      padding-bottom: 5px;
      font-size: 24px;
      color: #333333;
      margin: 0 10px;
      line-height: 28px;
    }
    & >.content {
      box-sizing: border-box;
      max-width: 405px;
      margin: 0 10px;
      line-height: 36px;
      border-radius: 10px;
    }
    & >.content.image{
      &>.image{
        max-width: 100%;
        height: auto;
      }
    }
    & > .content.navigator {
      &>.card{
        width: 400px;
        border-radius: 10px;
        background-color: #FFFFFF;
        box-sizing: border-box;
        border: 1px solid #eaeaea;
        display: flex;
        flex-direction: column;
        &> .image{
          width: 100%;
          height: 200px;
        }
        &> .title{
          font-size: 26px;
          padding: 5px 10px;
        }
      }
    }
    & > .content.text {
      background-color: #FFFFFF;
      font-size: 32px;
      word-break: break-all;
      padding: 17px 10px;
      white-space: pre-wrap;
      .phone{
        color: #6190E8;
      }
    }

    .voiceWrap{
      // padding-top: 12px;
      // padding-left: 10px;
      // height: 35px;
      // width: 150px;
      // background: #1bbc9b;
      // border-radius: 0 7px 7px;
      display: flex;
      align-items: center;
      font-size: 28px;
    }

    .voice {
      background:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAAAYCAYAAAAF6fiUAAAC50lEQVRogc2aT4hNURzHP2OYRhYKpZEiTcrG5vmTfztKzUYp6jErSiElK0MpmZIkRSn/srExZaEoIxvFQr1oMikLC6RJE5KUDE+nfq+O45zbOe+ec+79bX6/e857n/t793vu+ft6MKzRaJhFuU3l1E54z8XASeAFcDM3v9Vq/XM9O0ECZS21AOuAw9p1bBGC+LMi3zyG/UnMvw9ckfgGsLlKfh0EUDlsBeZk4iuBDwIP5PpRyXuX4scS4L+xJMCOS5KvgL5I+ejm4g8BX4F+aamV8GMJ0C4hgkp+BlgJPDXqygjrw98jfhhYlpi/3PblmF1Qtw/ruZp8SbwGOKHVxRiMi/iqm5iUeKQKvk2AM8BH4FBgPr4Py8afAI5p9fO1ulBhQ/nnxA97Nshu+XttfNsN5wEDwGXpx3zNVwAX/wIwJXGo+GX4d8XPBTbk5tsEOArclngswVqhiH9V/E6tLLQbCuV/B15KvDE33/XKqdflh6i2wyOpUHPxH4tfLXUdC+2GQvkT4gdz84v6vHHxmzyTCjUb/7141aqWZOR/Er8wN79IgJ/iewOSCjEbX+9uyk5BQ/htS1kWfpEA28Tru0cxp602/oAWT1HOQviLxH9JlL+T73qg54EFEo9p5bEGZBd/i/hJGbxy8VeJf5ubbxNgRJvT7pPBpmO/PRMssiL+AfH3tLLQriiU3ycLKGRRlZVvE2Cp9Flnja3U/kgCuPhNYIXEl4wfEDIVDeUPyZutftuT3HzXgYzqs6aNz8Xcozf56hDjgySqkj+i1amdxF8J+c9kgXQH2J2abx7IuMaAaeM69gGJzh+U0yOV/Dsj+X7Z6ErFX6utTk9Xwa/DeUBTZg9q63a9UTcTQfwifmcAfahtmmXl1+FI8qK0nHFj6tbbZev35V/TtqCbVfHrIMA34JZR1hNpwHfx1Uxmv8S7gM9V8et4JkziQ/ntwKjEp4x5fHZ+Hf8VkdreANeB17KFHNv8+cBftv8EfcpZBv8AAAAASUVORK5CYII=') right 0 no-repeat;
        width: 30px;
        height: 30px;
        background-size: 400%;
        margin-right: 10px;
      }

      .voicePlay {
        animation-name: voicePlay;
        animation-duration: 1s;
        animation-direction: normal;
        animation-iteration-count: infinite;
        animation-timing-function: steps(3);
      }

      @keyframes voicePlay {
        0% {
          background-position: 0;
        }
        100% {
          background-position: 100%;
        }
      }
      .playing{
        .voice{
          animation-name: voicePlay;
          animation-duration: 1s;
          animation-direction: normal;
          animation-iteration-count: infinite;
          animation-timing-function: steps(3);
        }
      }
  }
  &.right .body{
    .voiceWrap{
      flex-direction: row-reverse;
    }
    .voice{
      margin-left: 10px;
      margin-right: 0;
      transform: rotateY(180deg);
    }
  }
}
.replyContent{
  background-color: #FFFFFF;
  font-size: 32px;
  word-break: break-all;
  padding: 17px 10px;
  white-space: pre-wrap;
  margin: 0 10px;
  .newsItem{
    border-bottom: 1px solid #eeeeee;
    &:last-child{
      border-bottom: none;
    }
    .title{
      font-size: 32px;
      color: #333333;
    }
  }
  .firstNews{
    position: relative;
    .image{
      display: block;
      width: 100%;
    }
    .title{
      position: absolute;
      bottom: 0;
      left: 0;
      box-sizing: border-box;
      width: 100%;
      padding: 20px 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      background: rgba(0, 0, 0, .4);
      color: #ffffff;
    }
  }
  .restNews{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 0;
    .image{
      display: block;
      width: 60px;
      height: 60px;
    }
    .title{
      flex: 1;
      padding-right: 30px;
      padding-left: 10px;
    }
  }
}
.msgStatusIcon{
  line-height: 66px;
}
.messageNews,
.messageNews .replyContent,
.messageNews .newsItem,
.messageNews .firstNews{
  width: 100%;
  box-sizing: border-box;
  margin: 0;
}
