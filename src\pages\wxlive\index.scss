page {
  background: #F3F4F6;
}
.flex-wrap{
  padding: 20px 25px;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  background: #F3F4F6;
  justify-content: space-between;
}
.flex-item{
  width: 340px;
  border-radius: 20px;
  background: #fff;
  margin-bottom: 24px;
  position: relative;
  overflow: hidden;
  .room-img{
    height: 340px;
    width: 100%;
    overflow: hidden;
    image{
      width: 100%;
      height: 100%;
    }
  }
  .room-name{
    width: 100%;
    // height: 64px;
    box-sizing: border-box;
    padding: 0px 25px;
    margin: 18px 0;
    font-size: 24px;
    font-weight: bold;
    color: #333333;
    line-height: 32px;
    display: -webkit-box ;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp:2;
    /* autoprefixer: ignore next */
    -webkit-box-orient: vertical;
  }
  .live-status{
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 8px 20px;
    background: #FD222B;
    border-radius: 18px;
    font-size: 20px;
    font-weight: bold;
    color: #FFFFFF;
  }
  .replay{
    background: #A0A0A0;
    color: #FFFFFF;
  }
}
