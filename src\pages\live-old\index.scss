
.title {
  background: #fff;
  position: relative;
  z-index: 2;
  line-height: 80px;

  &:after {
    content: '';
    display: block;
    height: 1px;
    background: #eee;
  }

  .txt {
    padding-left: 30px;
    font-size: 30px;
    font-weight: 700;
  }
}

.list-wrap {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  padding-top: 482rpx;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;

  .list {
    height: 100%;    
    overflow-y: scroll; 
  }
  .item {
    padding: 10px 30px;
    &:after {
      content: '';
      display: block;
      height: 2px;
      background: #eee;
    }
    &:last-child {
      &:after {
        content: '';
        display: none;
        // height: 1px;
        // background: #eee;
      }
    }

    &.this {
      .item-title {
        // color: #388efd
        color: rgb(233, 104, 107)
      }
      .item-time {
        // color: #388efd
        color: rgb(233, 104, 107)
      }

      .mask {
        // background: rgb(233, 104, 107);
        // color: #fff;
      }
    }
  }
  .item-title {
    font-size: 30px;
    color: #666;
  }
  .item-time {
    font-size: 20px;
    color: #888;
    margin-bottom: 10px;
    margin-top: 8px;

    .mask {
      display: inline-block;
      padding: 4px 6px;
      font-size: 20px;
      border-radius: 4px;
      margin-right: 10px;
    }
  }

  .audio {
    display: none;
  }
}