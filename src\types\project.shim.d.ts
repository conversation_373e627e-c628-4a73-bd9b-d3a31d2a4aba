import TaroStatic from '@tarojs/taro';
import { ComponentClass } from 'react'
// import * as taro<PERSON> from "taro-ui";

declare module '@tarojs/taro' {
  interface TaroStatic {
    uma: any // 友盟统计
  }
  namespace getPrivacySetting {
    interface Option {
      /** 接口调用结束的回调函数（调用成功、失败都会执行） */
      complete?: (res: TaroGeneral.CallbackResult) => void;
      /** 接口调用失败的回调函数 */
      fail?: (res: TaroGeneral.CallbackResult) => void;
      /** 接口调用成功的回调函数 */
      success?: (result: SuccessCallbackResult) => void;
    }
    interface SuccessCallbackResult extends TaroGeneral.CallbackResult {
      /** 是否需要用户授权隐私协议（如果开发者没有在[mp后台-设置-服务内容声明-用户隐私保护指引]中声明隐私收集类型则会返回false；如果开发者声明了隐私收集，且用户之前同意过隐私协议则会返回false；如果开发者声明了隐私收集，且用户还没同意过则返回true；如果用户之前同意过、但后来小程序又新增了隐私收集类型也会返回true） */
      needAuthorization: boolean;
      /** 隐私授权协议的名称 */
      privacyContractName: string;
    }
  }
  interface TaroStatic {
    getPrivacySetting: (option?: getPrivacySetting.Option) => void;
  }
}
// declare module 'taro-ui' {

//   export interface AtModalProps {
//     children?: React.ReactNode
//   }
//   export interface AtComponent {
//     children?: React.ReactNode
//   }
// }

// export interface AtModalProps {
//   children?: React.ReactNode
// }
// export interface AtComponent {
//   children?: React.ReactNode
// }

// declare global {
//   interface AtModalProps {
//     children?: React.ReactNode
//   }
//   interface AtComponent {
//     children?: React.ReactNode
//   }
//   const AtModal: ComponentClass<AtModalProps&{children?: React.ReactNode}, any>
// }
// export const AtModal: ComponentClass<AtModalProps&{children?: React.ReactNode}, any>

