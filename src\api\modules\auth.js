import Taro from '@tarojs/taro';
import { getSessionId, setSessionId, setLoggedIn } from '@/gdata';
import API from '../api';
import { doGet, doPost } from '../request'

const isWeapp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP
const isAlipay = Taro.getEnv() === Taro.ENV_TYPE.ALIPAY
// 获取首页信息
API.extend({
  '/auth/check/user': '/Auth/xcx/phone/registe/status', // 判断用户是否是新用户
  '/auth/sns/code': '/Auth/v3/auth/verificate/code', // 短信验证

  '/auth/get/session': '/Auth/wxxcx/session/fetch', // 获取用户session
  '/auth/check/bind/status': '/Auth/xcx/bind/status/v1', // 检查绑定状态：1已绑定，0未绑定
  '/auth/login': '/Auth/xcx/bind/v1', // 绑定（用户登陆）
  '/auth/login/v2': '/Auth/xcx/bind/by/wx/phone', // 绑定（用户登陆）v2，通过获取小程序手机号，简化登录
  '/auth/login/chezhuka': '/Auth/xcx/bind/v2', // 绑定（用户登陆）
  '/auth/loginout': '/Auth/xcx/unbind', // 用户解绑


  '/alipay/auth/check/user': '/Auth/alipay/xcx/phone/registe/status', // 判断用户是否是新用户
  '/alipay/auth/login/sns/code': '/Auth/alipay/xcx/bind/v1', // 手机号验证码绑定（用户登陆）
  '/alipay/auth/get/session': '/Auth/alipay/xcx/session/fetch', // 获取用户session
  '/alipay/auth/check/bind/status': '/Auth/alipay/xcx/bind/status/v1', // 检查绑定状态：1已绑定，0未绑定
  '/alipay/auth/login': '/Auth/alipay/xcx/bind/by/alipay/phone', // 绑定（用户登陆）v2，通过获取小程序手机号，简化登录
  '/alipay/auth/loginout': '/Auth/alipay/xcx/unbind', // 用户解绑
});

// 微信用户解绑
export function loginOut() {
  if (isAlipay) return loginOutAlipay()
  return doPost(API.dget('/auth/loginout'), '');
}

// 支付宝用户解绑
export function loginOutAlipay() {
  return doPost(API.dget('/alipay/auth/loginout'), '');
}

// 获取session
function requestSession(code) {
  return doPost(API.dget('/auth/get/session'), { code: code });
}

// 获取alipay-session
function requestAlipaySession(code) {
  return doPost(API.dget('/alipay/auth/get/session'), { code: code });
}

// 获取sessionId
export function getSession(force = false) {
  return new Promise((resolve, reject) => {
    const sessionId = getSessionId();
    if (!force && sessionId) {
      resolve(sessionId);
      return;
    };
    Taro.login({}).then(res => {
      if (isAlipay) {
        requestAlipaySession(res.code).then(res2 => {
          setSessionId(res2.sessionid);
          resolve(res2.sessionid);
        })
      }
      requestSession(res.code).then(res2 => {
        setSessionId(res2.sessionid);
        // return res2.sessionid;
        resolve(res2.sessionid)
      })
    }).catch(err => {
      reject(err);
    })
  })
}

export function requestAuth(force = false) {
  return getSession(force).then(checkBindStatus).then(res => {
    if (!res || res.status != 1) return Promise.reject('未登录或绑定');
  })
}

// 判断当前微信是否绑定过微信
export function checkBindStatus() {
  // return doPost(API.dget('/auth/check/bind/status'), { wxUserJson: userInfo.toString()});
  if (isAlipay) {
    return checkAlipayBindStatus();
  }
  return doPost(API.dget('/auth/check/bind/status'), { });
}

// 判断当前支付宝帐号是否绑定过
export function checkAlipayBindStatus() {
  // return doPost(API.dget('/auth/check/bind/status'), { wxUserJson: userInfo.toString()});
  return doPost(API.dget('/alipay/auth/check/bind/status'), { });
}

// 用户登陆
export function authLogin(phoneNum, passWord, verifyCode, wxUser) {
  if (isAlipay) return authLoginAlipayPhone(phoneNum, verifyCode)
  const data = {
    phone: phoneNum,
    password: passWord,
    code: verifyCode,
    wxUserJson: JSON.stringify(wxUser)
  };
	return doPost(API.dget('/auth/login'), data);
}

// 支付宝用户登陆
export function authLoginAlipayPhone(phoneNum, verifyCode) {
  const data = {
    phone: phoneNum,
    code: verifyCode,
  };
	return doPost(API.dget('/alipay/auth/login/sns/code'), data);
}

/**
 * 用户手机号快捷登陆-整合微信、支付宝
 * @param {object} params 手机号、加密数据、用户资料信息JSON
 * @returns
 */
export function authLoginMerge(params) {
  if (isAlipay) {
    return authLoginAlipay(params)
  }else {
    return authLoginV2(params.iv, params.encryptedData, params.userInfo)
  }
}

/**
 * 微信快捷登陆v2
 * @param {string} iv 加密向量
 * @param {string} encryptedData 加密数据
 * @param {string} userInfo 用户资料信息JSON
 * @returns
 */
export function authLoginV2(iv, encryptedData, userInfo) {
  const data = {
		iv: iv,
		encryptedData: encryptedData,
    xcxToken: getSessionId(),
    wxUserJson: userInfo,
  };
	return doPost(API.dget('/auth/login/v2'), data);
}

/**
 * 支付宝快捷登陆
 * @param {object} params 加密信息
 * @returns
 */
export function authLoginAlipay(params) {
  // const data = {
	// 	phone: phone,
	// 	encryptedData: encryptedData,
  //   // xcxToken: getSessionId(),
  //   wxUserJson: userInfo,
  // };
	return doPost(API.dget('/alipay/auth/login'), params);
}

export function loginForCheZhuKa(phoneNum, wxUser) {
  const data = {
		phone: phoneNum,
    wxUserJson: JSON.stringify(wxUser)
  };
	return doPost(API.dget('/auth/login/chezhuka'), data);
}

// 获取短信验证码
export function getSNSCode(phone) {
  return doGet(API.dget('/auth/sns/code'), { value: phone, type: 1 });
}

// 检查当前手机号是否注册过账号
export function getCheckUser(phone) {
  if (isAlipay) return doPost(API.dget('/alipay/auth/check/user'), { phone: phone });
  return doPost(API.dget('/auth/check/user'), { phone: phone });
}

export const ret = {};
