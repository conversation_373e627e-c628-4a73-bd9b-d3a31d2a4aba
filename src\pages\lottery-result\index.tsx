import Taro, { useDidShow, usePullDownRefresh } from "@tarojs/taro";
import React, { useState, useEffect, useMemo } from "react";
import { View, Text } from "@tarojs/components";
import { Empty, Cell, Loading } from "@antmjs/vantui";
import dayjs from "dayjs";
import { getWinningResults } from "@/api/modules/lottery"; // 假设有这个API

import "./index.scss";

const ResultsPage = () => {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // useEffect(() => {
  //   debugger;
  //   fetchResults();
  // }, []);

  useDidShow(() => {
    fetchResults();
  });
  const fetchResults = async () => {
    try {
      const data = await getWinningResults(); // 获取中奖结果
      setResults(data);
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch results:", error);
      setLoading(false);
    }
  };

  const timeToDate = (time) => {
    return dayjs(time).format("YYYY-MM-DD HH:mm");
  };

  // 下拉刷新
  usePullDownRefresh(() => {
    fetchResults();
    Taro.stopPullDownRefresh();
  });

  // 使用useMemo缓存计算结果
  // const avaliableRecords = useMemo(
  //   () =>
  //     results.filter((record) => {
  //       return record.status == 2; // status为1表示未中奖，2表示中奖
  //     }) || [],
  //   [results]
  // );

  return (
    <View className="results-page">
      {loading ? (
        <View className="loading-container">
          <Loading type="spinner" size="24px">
            加载中...
          </Loading>
        </View>
      ) : results.length > 0 ? (
        results.map((result) => (
          <Cell
            className={result.prizeName ? "prize-cell" : ""}
            key={result.id}
            renderTitle={
              <View>
                <View>{result.activityName}</View>
                <View className="prize-name">{result.prizeName}</View>
              </View>
            }
            label={timeToDate(result.applyTime)}
            value={result.status == 2 ? "恭喜中奖" : "未中奖"}
          />
        ))
      ) : (
        <Empty description="暂无中奖信息" />
      )}
    </View>
  );
};

export default ResultsPage;
