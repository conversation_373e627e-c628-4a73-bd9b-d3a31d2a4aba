import Taro from '@tarojs/taro'

export function isWeapp() : boolean {
  return Taro.getEnv() === "WEAPP"
}
export function isH5() :boolean {
  return Taro.getEnv() === "WEB"
}


export function isInWeApp() :boolean {
  return  /miniProgram/i.test(navigator.userAgent); // 是否是微信小程序
}

export function isInWeixin() :boolean {
  return /MicroMessenger/i.test(navigator.userAgent) // 是否是微信环境
}

export function isInJglh() :boolean {
  return /jglh/i.test(navigator.userAgent) || /jgrm/i.test(navigator.userAgent);
}
export function isInWeixinH5() :boolean {
  return isInWeixin() && !isInWeApp(); // 是否是微信网页环境
}

export function isUnionPayMP() :boolean {
  return /com.unionpay/i.test(navigator.userAgent); // 是否是云闪付环境
}

export function isIOS() :boolean {
  return /iphone|ipad|ipod/i.test(navigator.userAgent);
}

export function isAndroid() :boolean {
  return /android/i.test(navigator.userAgent);
}

export function isProduction() :boolean {
  return /radio.jgrm.net/i.test(window.location.host);
}
