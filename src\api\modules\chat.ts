// import Taro from '@tarojs/taro';
import API from '../api';
import { doGet, doPost, doPostJson } from '../request'

// 获取首页信息
API.extend({
  '/ws/req-id': '/ws/req-id', // 判断用户是否是新用户
  '/template-id': 'Radio/program/radioinfo/v2?v=454&sid=HN0001',
  '/subscribe': '/Radio/chat/messages/v3',

  '/ws/messages': '/Radio/chat/messages/v3', // 根据聊天室id和时间查询消息的历史记录
  'room/list': '/Radio/program/radioinfo/v2?v=454&sid=HN0001',
  'qiniu/token': '/Radio/resource/cloud/bulk/gettoken?v=420', // 获取图片七牛上传token
  'qiniu/audio/token': '/Radio/resource/cloud/gettoken?type=0', // 获取音频七牛上传token
});

export const getReqId = (reqId) => {
  return doPost(API.dget('/ws/req-id'), { reqId });
}
export const getTemplateId = (id) => {
  return doGet(API.dget('/template-id'), { id });
}
export const getLiveRoomList = () => {
  return doGet(API.dget('room/list'), { });
}
/**
 * @description: 根据聊天室id和时间查询消息的历史记录
 * @param {String} roomId 当前聊天室id
 * @param {number} t 最后一条消息的时间戳
 * @param {number} max 请求最大条数,默认是10
 * @return {*}
 */
export const getMessages = (roomId: string, t?: number, max = 10) => {
  return doGet(API.dget('/ws/messages'), { roomId, t, max });
}
export const handleRead = (msgId: string) => {
  return doPostJson(API.dget('/ws/messages'), { msg_id: msgId, });
}
export const handleSubscribe = () => {
  return doPost(API.dget('/subscribe'), {});
}

function getQiniuToken() {
  return doGet(API.dget('qiniu/token')).then(function(res) {
    return res.token;
  })
}

function getQiniuAudioToken() {
  return doGet(API.dget('qiniu/audio/token')).then(function(res) {
    return res;
  })
}
/**
 * 获取七牛上传token
 * @param {type} 1 代表图片，2 代表音频 默认1
 */
export function initQiniuUploadToken (type = 1) {
  return new Promise<any>((resolve, reject) => {
    var TOKEN = '';
    // token有效期1小时，这里每 55分钟获取一次
    var INTERVAL = 1000 * 60 * 55;

    // 获取失败后的重试间隔6s
    var RETRY_INTERVAL = 1000 * 6;

    var FAIL_COUNT = 0;

    const getToken = ():any => {
      if(type == 1){
        return getQiniuToken().then((res) => {
          FAIL_COUNT = 0;
          return res
        }).catch(function () {
          FAIL_COUNT++;
        })
      }else if(type == 2){
        return getQiniuAudioToken().then((res) => {
          return res
          FAIL_COUNT = 0;
        }).catch(function () {
          FAIL_COUNT++;
        })
      }
    }

    function shouldTip() {
      return FAIL_COUNT >= 10 && FAIL_COUNT % 10 == 0
    }

    function timerGetToken(delay) {
      delay = delay || INTERVAL
      setTimeout(function () {
        getToken().then(function (t) {
          TOKEN = t;
          resolve(TOKEN)
          timerGetToken(INTERVAL)
        }).catch(function (msg) {
          console.error(msg);
          if (shouldTip()) {
            reject('获取TOEKN失败')
            console.error('获取TOEKN失败')
          }
          timerGetToken(RETRY_INTERVAL)
        })
      }, delay)
    }

    // 首次获取
    timerGetToken(1);
  })
};
