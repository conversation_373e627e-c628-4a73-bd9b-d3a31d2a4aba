import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { View, Image, Swiper, SwiperItem } from '@tarojs/components'
import { getFullImagePath } from '../../../../tools'

import './index.scss'


interface PicLoop {
  articleTitle: string
  endTime: any
  id: number
  image: string
  permCode: number
  sid: string
  sort: number
  startTime: any
  title: string
  type: number
  url: string
}

interface Props {
  picLoop: Pic<PERSON>oop[],
  toPage: Function
}

interface State {
  current: number
}

export default class Index extends Component<Props, State> {


  // config: PageConfig = {
  //   navigationBarTitleText: '首页',
  // }

  constructor(props) {
    super(props);

    this.state = {
      current: 0
    };
  }

  componentWillMount () {
  }

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidShow () {


  }

  componentDidHide () { }
  toPage(url: string) {
    this.props.toPage(url);
  }

  changeEnd(ev) {
    this.setState({
      current: ev.currentTarget.current
    });
  }


  render () {
    let { picLoop } = this.props;

    return (
      <View className="sub-banner">
        <Swiper
          className="sub-swuper"
          circular
          onChange={this.changeEnd.bind(this)}
        >
          {
            picLoop.map((item) => {
              return <SwiperItem key={item.id} onClick={this.toPage.bind(this,  item.url)}>
                  <Image className="img" src={getFullImagePath(item.image)} />
              </SwiperItem>
            })
          }
        </Swiper>
        <View className="dots">
          {
            picLoop.length > 1 &&
            picLoop.map((item, index) => {
              return <View key={item.id} className={['dot', index === this.state.current ? 'this' : null].join()} />
            })
          }
        </View>
      </View>
    )
  }
}
