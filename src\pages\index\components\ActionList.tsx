import Taro from "@tarojs/taro";
import React, { Component } from "react";
import { View, Image, Text } from "@tarojs/components";
import dayjs from "dayjs";

import { getFullImagePath } from "@/tools";

import "./ActionList.scss";

// import { getActionList } from "@/api/modules/home";

interface Action {
  applyEndTime: number;
  applyStartTime: number;
  id: number;
  payEndTime: number;
  promotion: number;
  promotionImage: any;
  shareContent: string;
  shareUrl: string;
  sort: number;
  t: number;
  thumb: string;
  title: string;
  typeflag: number;
  url: string;
}

interface Props {
  toPage: Function;
  list: Action[];
}

interface State {
  actionList: Action[];
}

export default class ActionList extends Component<Props, State> {
  // config: PageConfig = {
  //   navigationBarTitleText: '首页',
  // }

  constructor(props) {
    super(props);

    this.state = {
      actionList: [],
    };
  }

  componentDidMount() {
    // getActionList().then((res) => {
    //   this.setState({
    //     actionList: res,
    //   });
    // });
  }

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}
  toPage(url: string) {
    this.props.toPage(url);
  }

  timeToDate(time) {
    return dayjs(time).format("YYYY-MM-DD");
  }
  render() {
    let actions = this.props.list || [];
    // const actions = actionList.filter(item => {
    //   return item.title.indexOf('驾考团') === -1;
    // })
    return (
      <View className="active-list">
        <View className="active-title">
          <View className="active-img">交广活动</View>
          <View className="more">
            {/* <Text >查看更多</Text> */}
            {/* <Image className='img' src={go}></Image> */}
          </View>
        </View>
        <View className="w">
          {actions.map((item) => {
            return (
              <View
                className="list-item"
                key={item.id}
                onClick={this.toPage.bind(this, item.url)}
              >
                <View className="item">
                  <View className="img-w">
                    <Image
                      className="img"
                      src={getFullImagePath(
                        item.thumb,
                        "?imageView2/0/format/jpg/imageslim"
                      )}
                    />
                  </View>
                  <Text className="title">{item.title}</Text>
                  <Text className="time">{this.timeToDate(item.t)}</Text>
                </View>
              </View>
            );
          })}
        </View>
        {!actions.length && (
          <View className="empty-tip">
            <Text>暂无活动，敬请关注</Text>
          </View>
        )}
      </View>
    );
  }
}
