import Taro from '@tarojs/taro'
import React, { Component }  from 'react'
import { View } from '@tarojs/components'
import { AtNavBar } from 'taro-ui'


import './IndexLayout.scss'

interface Props {
  children?: any;
  title: string;
  leftShow?: boolean;
  leftUrl?: string
}

interface State {
  statusBarHeight: number | undefined,
  height: number
}

export default class IndexLayout extends Component<Props, State> {


  // config: PageConfig = {
  //   navigationBarTitleText: '首页',
  // }

  constructor(props) {
    super(props);

    this.state = {
      statusBarHeight: 0,
      height: 0,

    };
  }

  // 计算navbarheight
  computedNavBarHeight() {
    let rect = Taro.getMenuButtonBoundingClientRect ? Taro.getMenuButtonBoundingClientRect() : { top: 0, height: 0 };
    let res = Taro.getSystemInfoSync();
    console.log(res, rect);
    // let pH = res.screenHeight - res.windowHeight - 20;
    let navBarHeight = (function () {
      let gap = rect.top - res?.statusBarHeight;
      return res?.statusBarHeight + 2 * gap + rect.height;
    })();
    let ios = !!(res.system.toLowerCase().search('ios') + 1);
    let navBarExtendHeight = 0;
    if (ios) {
      navBarExtendHeight = 4; // 下方扩展4像素高度 防止下方边距太小
    }

    this.setState({
      statusBarHeight: res.statusBarHeight,
      height: navBarHeight + navBarExtendHeight,
    });
  }


  componentDidMount () {
    this.computedNavBarHeight();
  }

  componentWillUnmount () { }

  componentDidShow () {
  }

  componentDidHide () { }

  showTip(str) {
    Taro.showToast({
      title: str,
      icon: 'none'
    });
  }

  showLoading(title) {
    Taro.showLoading({
      title: title,
      mask: true
    });
  }

  hideLoading() {
    Taro.hideLoading();
  }

  showModal(title:string, msg:string) {
    Taro.showModal({
      title: title,
      content: msg,
      showCancel: false
    });
  }

  handleClick(leftIconType) {
    if (leftIconType) {
      if (this.props.leftUrl) {
        Taro.switchTab({
          url: this.props.leftUrl
        });
      } else {
        Taro.navigateBack();
      }
    }
  }

  render () {
    const { statusBarHeight, height } = this.state;
    let { children, title, leftShow } = this.props;
    let navigationStyle = `padding-top: ${statusBarHeight}px;height: ${height}px;box-sizing: border-box;background: #398eff`;
    let nabBarBgStyle = `background-color: transparent !important;top: ${statusBarHeight}px`
    let leftIconType = leftShow ? 'chevron-left' : '';

    return (
      <View className="index" style={`padding-top: ${height}px;`}>
        <View className="navbar" style={navigationStyle}>
          <AtNavBar
            className="navbar2"
            color="#fff"
            border={false}
            fixed
            leftIconType={leftIconType}
            onClickLeftIcon={this.handleClick.bind(this, leftIconType)}
            customStyle={nabBarBgStyle}
          >
            <View className="title">{title}</View>
          </AtNavBar>
        </View>
        {children}
      </View>
    )
  }
}
