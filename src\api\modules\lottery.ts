import Taro from "@tarojs/taro";
import API from "../api";
import { doGet, doPost } from "../request";

const isWeapp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP;
const isAlipay = Taro.getEnv() === Taro.ENV_TYPE.ALIPAY;
// 获取首页信息
API.extend({
  "/order/find/by/orderid": "/Radio/order/find/by/orderid", // 订单状态：1-未支付，2-已支付，-1-已退款，-2-部分退款
  "/lottery/winning/results": "/Radio/lotteryAssistant/activity/apply/list", // 中奖结果
  "/lottery/subscribe": "/Radio/lotteryAssistant/activity/apply", // 订阅抽奖活动
  "/lottery/detail": "/Radio/lotteryAssistant/activity/get", // 抽奖活动详情
});

// 获取抽奖活动详情
export function getLotteryDetail(id) {
  return doGet(API.dget("/lottery/detail"), { id });
}

// 订阅抽奖活动
export function subscribeLottery(activityId) {
  return doPost(API.dget("/lottery/subscribe"), { activityId });
}

// 获取中奖结果
export function getWinningResults() {
  return doGet(API.dget("/lottery/winning/results"));
}
