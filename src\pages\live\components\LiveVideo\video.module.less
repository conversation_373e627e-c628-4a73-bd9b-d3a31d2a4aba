.liveView{
  position: relative;
  overflow: scroll;
  flex-shrink: 0;
  height:35vh;
  .video{
    width:100vw;
    height:35vh;
    display: block;
    position: relative;
  }
}

.liveControlWrap{
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  color: #fefefe;
  font-size: 30rpx;
  overflow: hidden;
  .onlineCount{
    position: absolute;
    right: 30px;
    top: 30px;
    height: 44px;
    line-height: 44px;
    box-sizing: border-box;
    padding: 0 20px;
    font-size: 24px;
    border-radius: 22px;
    background: rgba(0,0,0, .4);
    color: #ffffff;
    z-index: 89;
  }
  .livePoster{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: url(https://img.jgrm.net/Ftxxsiy941J0hDLSfRAIrOrZ6h8A) no-repeat center;
    background-size: cover;
    z-index: 86;
  }
  .liveControl{
    position: relative;
    background: linear-gradient(#000000 0%, rgba(0,0,0, .1) 20%, rgba(0,0,0, .1) 80%, #000000 100%);
    width: 100%;
    height: 100%;
  }
  .playBtn {
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: .6;
    z-index: 88;
  }
  .fullScreen {
    color: #fefefe;
    position: absolute;
    right: 15px;
    bottom: 20px;
    opacity: .6;
    z-index: 88;
  }
  .back {
    color: #ffffff;
    position: absolute;
    left: 30px;
    top: 30px;
    opacity: .6;
    z-index: 88;
  }
  .channelWrap{
    box-sizing: border-box;
    // pointer-events: none;
  }
  .channel{
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 89;
    width: 80%;
    padding: 20px 10px;
    box-sizing: border-box;
    // display: flex;
    // align-items: center;
    // flex-wrap: nowrap;
    // overflow-x: scroll;
    white-space: nowrap;
    animation-name: fadeInUp;
    animation-duration: .6s;
    animation-fill-mode: both;
    opacity: 1;
    // pointer-events: none;
    &::-webkit-scrollbar {
      display: none;
      // width:0;
      // height:0;
      // color:transparent;
    }
    .channelItem{
      display: inline-block;
      border-radius: 17px;
      padding: 0px 10px;
      background: rgba(0,0,0, .7);
      color: #ffffff;
      font-size: 24px;
      margin-right: 8px;
      word-break: break-all;
      white-space: nowrap;
      line-height: 1.5;
      &.active{
        background: #fd4925;
        color: #ffffff;
      }
    }
    // &.showChannel{
    //   opacity: 1;
    //   pointer-events: auto;
    // }
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0,100%,0);
    transform: translate3d(0,100%,0)
  }

  100% {
      opacity: 1;
      -webkit-transform: none;
      transform: none
  }
}
