import Taro from "@tarojs/taro";
import React, { Component } from "react";
import { View, Image, Icon } from "@tarojs/components";
import { getFullImagePath } from "@/tools";

import "./index.scss";

interface FrameAd {
  url: string;
  imageId: string;
}

interface Props {
  timer?: boolean;
  frameAd: FrameAd;
  toPage: Function;
  close: Function;
}
interface State {
  current: number;
  seconds: number;
}

export default class Index extends Component<Props, State> {
  constructor(props) {
    super(props);

    this.state = {
      current: 0,
      seconds: 10,
    };
  }

  componentWillMount() {}

  componentDidMount() {
    this.props.timer && this.handleCountDown();
  }

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}
  toPage(url: string) {
    // this.props.close();
    this.props.toPage(url);
  }

  handleCountDown() {
    if (this.state.seconds > 0) {
      setTimeout(() => {
        this.setState(
          (prevState) => ({ seconds: prevState.seconds - 1 }),
          () => {
            // 在状态更新完成后执行的操作
            this.handleCountDown();
          }
        );
        // this.setState({
        //   seconds: this.state.seconds - 1
        // })
        // this.handleCountDown()
      }, 1000);
    } else {
      this.setState({
        seconds: 0,
      });
      this.handleClose();
    }
  }
  handleClose() {
    this.props.close();
  }

  render() {
    let { frameAd, timer } = this.props;
    let { seconds } = this.state;
    // , '?imageView2/0/format/jpg/imageslim'
    return (
      <View className="pop-ad-wrap">
        <View className="pop-ad">
          {!!timer && (
            <View
              className={`pop-count-down ${
                seconds > 0 ? "" : "pop-count-down-hide"
              }`}
            >
              {seconds}S
            </View>
          )}
          <Image
            mode="widthFix"
            src={getFullImagePath(frameAd && frameAd.imageId, "?")}
            onClick={this.toPage.bind(this, frameAd.url)}
          />
          <View className="close-btn">
            <View onClick={this.handleClose.bind(this)}>
              <Icon size="32" type="cancel" color="#ffffff" />
            </View>
          </View>
        </View>
      </View>
    );
  }
}
